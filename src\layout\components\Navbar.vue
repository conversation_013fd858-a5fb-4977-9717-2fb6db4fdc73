<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />

    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!topNav"/>
    <top-nav id="topmenu-container" class="topmenu-container" v-if="topNav"/>

    <!-- 项目切换区域 -->
    <div class="project-switch-container">
      <div
        class="project-switcher"
        @click="switchProject"
        :title="currentProject ? `当前项目：${currentProject.name}，点击切换项目` : '点击选择项目'"
      >
        <div class="project-info">
          <i class="el-icon-office-building"></i>
          <span class="project-name">{{ currentProject ? currentProject.name : '请选择项目' }}</span>
        </div>
        <i class="el-icon-switch-button switch-icon"></i>
      </div>
    </div>

    <div class="right-menu">
      <template v-if="device!=='mobile'">
        <search id="header-search" class="right-menu-item" />

        <el-tooltip content="源码地址" effect="dark" placement="bottom">
          <ruo-yi-git id="ruoyi-git" class="right-menu-item hover-effect" />
        </el-tooltip>

        <el-tooltip content="文档地址" effect="dark" placement="bottom">
          <ruo-yi-doc id="ruoyi-doc" class="right-menu-item hover-effect" />
        </el-tooltip>

        <screenfull id="screenfull" class="right-menu-item hover-effect" />

        <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>

      </template>

      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <img :src="avatar" class="user-avatar">
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/user/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <el-dropdown-item @click.native="setting = true">
            <span>布局设置</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import Search from '@/components/HeaderSearch'
import RuoYiGit from '@/components/RuoYi/Git'
import RuoYiDoc from '@/components/RuoYi/Doc'

export default {
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
    RuoYiGit,
    RuoYiDoc
  },
  data() {
    return {
      currentProject: null
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'device'
    ]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      }
    }
  },
  mounted() {
    this.getCurrentProject()
  },
  methods: {
    // 获取当前项目信息
    getCurrentProject() {
      try {
        const selectedProject = localStorage.getItem('selectedProject')
        if (selectedProject) {
          this.currentProject = JSON.parse(selectedProject)
        }
      } catch (error) {
        console.error('获取当前项目信息失败:', error)
        this.currentProject = null
      }
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    // 切换项目
    switchProject() {
      this.$confirm('确定要切换项目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清除当前项目信息
        localStorage.removeItem('projectId')
        localStorage.removeItem('selectedProject')
        // 跳转到项目选择页面
        this.$router.push('/jump')
      }).catch(() => {});
    },
    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          location.href = process.env.VUE_APP_CONTEXT_PATH + "index";
        })
      }).catch(() => {});
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(90deg, rgba(26, 39, 68, 0.95) 0%, rgba(15, 27, 46, 0.95) 100%);
  backdrop-filter: blur(20px);
  box-shadow: 0 2px 10px rgba(0, 255, 255, 0.1);
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;

    &:hover {
      background: rgba(255, 255, 255, 0.05);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .project-switch-container {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    height: 100%;

    .project-switcher {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 10px 20px;
      background: linear-gradient(135deg, rgba(0, 255, 255, 0.15), rgba(0, 150, 255, 0.1));
      border: 1px solid rgba(0, 255, 255, 0.4);
      border-radius: 25px;
      color: rgba(255, 255, 255, 0.95);
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      user-select: none;
      backdrop-filter: blur(15px);
      position: relative;
      overflow: hidden;
      box-shadow:
        0 4px 15px rgba(0, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);

      // 添加发光边框动画
      &::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, #00ffff, #0099ff, #66ccff, #00ffff);
        border-radius: 25px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.4s ease;
        background-size: 300% 300%;
        animation: gradientShift 3s ease infinite;
      }

      // 内部光效
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.6s ease;
      }

      &:hover {
        background: linear-gradient(135deg, rgba(0, 255, 255, 0.25), rgba(0, 150, 255, 0.2));
        border-color: rgba(0, 255, 255, 0.6);
        transform: translateY(-2px) scale(1.02);
        box-shadow:
          0 8px 25px rgba(0, 255, 255, 0.3),
          0 0 20px rgba(0, 255, 255, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.3);
        color: #ffffff;

        &::before {
          opacity: 1;
        }

        &::after {
          left: 100%;
        }
      }

      &:active {
        transform: translateY(-1px) scale(1.01);
        transition: all 0.1s ease;
      }

      .project-info {
        display: flex;
        align-items: center;
        gap: 10px;
        position: relative;
        z-index: 1;

        i {
          font-size: 18px;
          color: #00ccff;
          filter: drop-shadow(0 0 8px rgba(0, 204, 255, 0.6));
          transition: all 0.3s ease;
        }

        .project-name {
          max-width: 180px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-weight: 600;
          letter-spacing: 0.5px;
          text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
        }
      }

      .switch-icon {
        font-size: 16px;
        color: #00ccff;
        opacity: 0.9;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        filter: drop-shadow(0 0 6px rgba(0, 204, 255, 0.5));
        position: relative;
        z-index: 1;
      }

      &:hover .switch-icon {
        transform: rotate(180deg) scale(1.1);
        opacity: 1;
        color: #66ccff;
        filter: drop-shadow(0 0 12px rgba(102, 204, 255, 0.8));
      }

      &:hover .project-info i {
        transform: scale(1.1);
        color: #66ccff;
        filter: drop-shadow(0 0 12px rgba(102, 204, 255, 0.8));
      }
    }
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: rgba(255, 255, 255, 0.8);
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: all .3s;
        border-radius: 4px;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.95);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
          color: rgba(255, 255, 255, 0.8);
          transition: color 0.3s ease;

          &:hover {
            color: rgba(255, 255, 255, 0.95);
          }
        }
      }
    }
  }
}
</style>
