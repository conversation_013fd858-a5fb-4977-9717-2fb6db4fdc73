<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="能源类型" prop="energyType">
        <el-select
          v-model="queryParams.energyType"
          placeholder="能源类型"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="dict in dict.type.energy_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="计费类型" prop="rateType">
        <el-select v-model="queryParams.rateType" placeholder="请选择计费类型" clearable>
          <el-option label="标准计费" value="0"/>
          <el-option label="阶梯计费" value="1"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['biz:billingConfig:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['biz:billingConfig:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['biz:billingConfig:remove']"
        >删除
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="billingConfigList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="名称" align="center" prop="name"/>
      <el-table-column label="能源类型" align="center" prop="energyType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.energy_type" :value="scope.row.energyType"/>
        </template>
      </el-table-column>
      <el-table-column label="计费类型" align="center" prop="rateType">
        <template slot-scope="scope">
          <span>{{ rateTypeFormat(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="峰平谷" align="center" prop="peakValleyFlag">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.yes_no" :value="scope.row.peakValleyFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="能源单位" align="center" prop="unit"/>
      <el-table-column label="起始有效期" align="center" prop="startTime" width="180"/>
      <el-table-column label="截止有效期" align="center" prop="endTime" width="180"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['biz:billingConfig:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['biz:billingConfig:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改计费配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 100%;">
        <el-tabs v-model="activeTab" style="height: 500px">
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入名称" style="width: 100%"/>
            </el-form-item>
            <el-form-item label="能源类型" prop="energyType">
              <el-select
                v-model="form.energyType"
                placeholder="能源类型"
                clearable
                style="width: 100%"
                @change="handleEnergyTypeChange"
              >
                <el-option
                  v-for="dict in dict.type.energy_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
        </el-form-item>
            <el-form-item label="计费类型" prop="rateType">
              <el-select v-model="form.rateType" placeholder="请选择计费类型" @change="handleRateTypeChange"
                         style="width: 100%"
              >
                <el-option label="标准计费" value="0"/>
                <el-option label="阶梯计费" value="1"/>
              </el-select>
        </el-form-item>
            <el-form-item label="峰平谷" prop="peakValleyFlag">
              <el-radio-group v-model="form.peakValleyFlag" @change="handlePeakValleyFlagChange" style="width: 100%">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
        </el-form-item>
            <el-form-item label="标准单价" prop="standardPrice" v-if="form.rateType === '0'">
              <el-input-number v-model="form.standardPrice" :min="0" :max="999" :precision="2" :step="0.1"></el-input-number>
        </el-form-item>
        <el-form-item label="能源单位" prop="unit">
              <el-input v-model="form.unit" placeholder="请输入能源单位" style="width: 100%" :disabled="true"/>
        </el-form-item>
        <el-form-item label="起始有效期" prop="startTime">
          <el-date-picker clearable
                          v-model="form.startTime"
                              type="date"
                          value-format="yyyy-MM-dd HH:mm:ss"
                              placeholder="请选择起始有效期"
                              style="width: 100%"
              >
          </el-date-picker>
        </el-form-item>
            <el-form-item label="截止有效期" prop="endTime">
          <el-date-picker clearable
                          v-model="form.endTime"
                              type="date"
                          value-format="yyyy-MM-dd HH:mm:ss"
                              placeholder="请选择截止有效期"
                              style="width: 100%"
              >
          </el-date-picker>
        </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="分段配置" name="ladder" v-if="form.peakValleyFlag === '1'">
            <el-button type="primary" size="mini" plain @click="addLadderConfig">添加时段</el-button>
            <div style="margin-top: 10px; color: #909399; font-size: 12px;">
              <i class="el-icon-info"></i> 提示：时段必须连续设置，先完成当前时段才能添加下一个时段
            </div>
            <el-table
              :data="form.peakValleyConfigs"
              style="margin-top: 10px; width: 100%;"
              :max-height="400"
              border
            >
              <el-table-column label="开始时间" align="center" prop="startTime" min-width="25%">
                <template slot-scope="scope">
                  <el-time-picker
                    v-model="scope.row.startTime"
                    value-format="HH:mm:ss"
                    placeholder="开始时间"
                    style="width: 100%"
                    :disabled="true"
                  >
                  </el-time-picker>
                </template>
              </el-table-column>
              <el-table-column label="结束时间" align="center" prop="endTime" min-width="25%">
                <template slot-scope="scope">
                  <el-time-picker
                    v-model="scope.row.endTime"
                    value-format="HH:mm:ss"
                    placeholder="结束时间"
                    style="width: 100%"
                    @change="validateTimeSegment(scope.row, scope.$index)"
                  >
                  </el-time-picker>
                </template>
              </el-table-column>
              <el-table-column label="类型" align="center" prop="type" min-width="20%">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.type" placeholder="请选择类型" style="width: 100%">
                    <el-option label="平段" value="0"/>
                    <el-option label="谷段" value="1"/>
                    <el-option label="峰段" value="2"/>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="加价" align="center" prop="price" min-width="20%">
                <template slot-scope="scope">
                  <el-input-number
                    v-model="scope.row.price"
                    :precision="2"
                    :step="0.01"
                    :min="-20"
                    :max="999999999"
                    style="width: 100%"
                  >
                  </el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" min-width="10%">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="danger"
                    icon="el-icon-delete"
                    @click="removeLadderConfig(scope.$index)"
                  >
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane label="阶梯配置" name="tiered" v-if="form.rateType === '1'">
            <el-button type="primary" size="mini" plain @click="addTieredConfig">添加阶梯</el-button>
            <div style="margin-top: 10px; color: #909399; font-size: 12px;">
              <i class="el-icon-info"></i> 提示：阶梯必须连续设置，先完成当前阶梯才能添加下一个阶梯
            </div>
            <el-table
              :data="form.tieredConfigs"
              style="margin-top: 10px; width: 100%;"
              :max-height="400"
              border
            >
              <el-table-column label="阶梯" align="center" prop="stage" min-width="15%">
                <template slot-scope="scope">
                  <el-input-number
                    v-model="scope.row.stage"
                    :min="1"
                    :precision="0"
                    style="width: 100%"
                    :disabled="true"
                  >
                  </el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="基价" align="center" prop="rate" min-width="20%">
                <template slot-scope="scope">
                  <el-input-number
                    v-model="scope.row.rate"
                    :precision="2"
                    :step="0.1"
                    :min="0"
                    :max="999"
                    style="width: 100%"
                  >
                  </el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="开始用量" align="center" prop="startEnergy" min-width="25%">
                <template slot-scope="scope">
                  <el-input-number
                    v-model="scope.row.startEnergy"
                    :min="0"
                    :max="999999999"
                    style="width: 100%"
                    :disabled="true"
                  >
                  </el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="结束用量" align="center" prop="endEnergy" min-width="25%">
                <template slot-scope="scope">
                  <el-input-number
                    v-model="scope.row.endEnergy"
                    :min="0"
                    :max="999999999"
                    style="width: 100%"
                    @change="validateEnergyRange(scope.row, scope.$index)"
                    :disabled="scope.$index === form.tieredConfigs.length - 1"
                  >
                  </el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" min-width="15%">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="danger"
                    icon="el-icon-delete"
                    @click="removeTieredConfig(scope.$index)"
                  >
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listBillingConfig,
  getBillingConfig,
  delBillingConfig,
  addBillingConfig,
  updateBillingConfig
} from '@/api/biz/billingConfig'

export default {
  name: 'BillingConfig',
  dicts: ['yes_no', 'energy_type'],
  data() {
    return {
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计费配置表格数据
      billingConfigList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 活动选项卡
      activeTab: 'basic',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        energyType: undefined,
        rateType: undefined,
        peakValleyFlag: undefined,
        standardPrice: undefined,
        unit: undefined,
        startTime: undefined,
        endTime: undefined
      },
      // 表单参数
      form: {
        id: undefined,
        name: undefined,
        energyType: undefined,
        rateType: undefined,
        peakValleyFlag: undefined,
        standardPrice: undefined,
        unit: undefined,
        startTime: undefined,
        endTime: undefined,
        peakValleyConfigs: [],
        tieredConfigs: []
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: '名称不能为空', trigger: 'blur' }
        ],
        energyType: [
          { required: true, message: '能源类型不能为空', trigger: 'change' }
        ],
        rateType: [
          { required: true, message: '计费类型不能为空', trigger: 'change' }
        ],
        peakValleyFlag: [
          { required: true, message: '是否分段不能为空', trigger: 'blur' }
        ],
        unit: [
          { required: true, message: '能源单位不能为空', trigger: 'blur' }
        ],
        startTime: [
          { required: true, message: '起始有效期不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 计费类型格式化
    rateTypeFormat(row) {
      return row.rateType === '0' ? '标准计费' : '阶梯计费'
    },
    /** 能源类型改变事件 */
    handleEnergyTypeChange(value) {
      // 根据能源类型设置对应的单位
      const energyTypeMap = {
        '1': 'm³',     // 水
        '2': 'kWh',    // 电
        '3': 'm³'      // 气
      }
      this.form.unit = energyTypeMap[value] || ''
    },
    /** 查询计费配置列表 */
    getList() {
      this.loading = true
      listBillingConfig(this.queryParams).then(response => {
        this.billingConfigList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        energyType: undefined,
        rateType: undefined,
        peakValleyFlag: undefined,
        standardPrice: undefined,
        unit: undefined,
        startTime: undefined,
        endTime: undefined,
        peakValleyConfigs: [],
        tieredConfigs: []
      }
      this.resetForm('form')
      this.activeTab = 'basic'
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加计费配置'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true
      this.reset()
      const id = row.id || this.ids
      getBillingConfig(id).then(response => {
        this.loading = false
        this.form = response.data
        // 确保有分段配置和阶梯配置数组
        if (!this.form.peakValleyConfigs) {
          this.form.peakValleyConfigs = []
        }
        if (!this.form.tieredConfigs) {
          this.form.tieredConfigs = []
        }
        // 根据能源类型设置对应的单位（编辑时）
        if (this.form.energyType) {
          this.handleEnergyTypeChange(this.form.energyType)
        }
        this.open = true
        this.title = '修改计费配置'
      })
    },
    // 验证时间段连续性
    validateTimeSegment(row, index) {
      // 验证结束时间必须大于开始时间
      if (!row.startTime) {
        this.$message.error('请先设置开始时间');
        row.endTime = null;
        return;
      }

      if (!row.endTime) {
        return;
      }

      const startTimeArr = row.startTime.split(':').map(Number);
      const endTimeArr = row.endTime.split(':').map(Number);

      const startSeconds = startTimeArr[0] * 3600 + startTimeArr[1] * 60 + startTimeArr[2];
      let endSeconds = endTimeArr[0] * 3600 + endTimeArr[1] * 60 + endTimeArr[2];

      // 特殊处理00:00:00，将其视为一天的结束
      if (endTimeArr[0] === 0 && endTimeArr[1] === 0 && endTimeArr[2] === 0) {
        endSeconds = 24 * 3600;
      }

      if (endSeconds <= startSeconds) {
        this.$message.error('结束时间必须大于开始时间');

        // 自动设置一个合理的结束时间
        const startHour = startTimeArr[0];
        const endHour = Math.min(startHour + 1, 24);
        row.endTime = endHour === 24 ? '00:00:00' : `${endHour.toString().padStart(2, '0')}:00:00`;
        return;
      }

      // 如果是最后一个时间段，检查结束时间是否为00:00:00
      if (index === this.form.peakValleyConfigs.length - 1) {
        if (endTimeArr[0] !== 0 || endTimeArr[1] !== 0 || endTimeArr[2] !== 0) {
          this.$message.error('最后一个时间段必须到00:00:00结束');
          row.endTime = '00:00:00';
          return;
        }
      }
    },

    // 验证整个表单提交前的分段配置完整性
    validatePeakValleyConfigs() {
      if (this.form.peakValleyFlag !== '1' || this.form.peakValleyConfigs.length === 0) {
        return true;
      }

      // 检查是否有未完成的配置
      const hasIncomplete = this.form.peakValleyConfigs.some(item =>
        !item.startTime || !item.endTime || !item.type || item.price === null || item.price === undefined
      );

      if (hasIncomplete) {
        this.$message.error('请完成所有分段配置');
        return false;
      }

      // 检查第一个时间段是否从00:00:00开始
      if (this.form.peakValleyConfigs[0].startTime !== '00:00:00') {
        this.$message.error('第一个时间段必须从00:00:00开始');
        return false;
      }

      // 检查最后一个时间段是否到00:00:00结束
      const lastConfig = this.form.peakValleyConfigs[this.form.peakValleyConfigs.length - 1];
      if (lastConfig.endTime !== '00:00:00') {
        this.$message.error('最后一个时间段必须到00:00:00结束');
        return false;
      }

      // 检查时间段是否连续
      for (let i = 0; i < this.form.peakValleyConfigs.length - 1; i++) {
        if (this.form.peakValleyConfigs[i].endTime !== this.form.peakValleyConfigs[i + 1].startTime) {
          this.$message.error('时间段必须连续');
          return false;
        }
      }

      return true;
    },

    // 添加阶梯配置
    addTieredConfig() {
      // 检查是否有未完成的配置
      if (this.form.tieredConfigs.length > 0) {
        const lastConfig = this.form.tieredConfigs[this.form.tieredConfigs.length - 1];

        // 检查最后一项是否已完成（除了endEnergy外的其他字段）
        if (lastConfig.rate === null ||
            lastConfig.rate === undefined ||
            lastConfig.rate === '' ||
            lastConfig.rate <= 0 ||
            lastConfig.startEnergy === null ||
            lastConfig.startEnergy === undefined ||
            lastConfig.startEnergy === '') {
          this.$message.warning("请先完成当前阶梯的配置");
          return;
        }

        // 获取上一个阶梯的开始用量
        const startEnergy = lastConfig.startEnergy;

        // 设置上一个阶梯的结束用量（不再是0）
        lastConfig.endEnergy = startEnergy + 100;

        this.form.tieredConfigs.push({
          stage: this.form.tieredConfigs.length + 1,
          rate: 1.0,
          startEnergy: lastConfig.endEnergy,
          endEnergy: 0 // 最后一段默认为0
        });
      } else {
        // 添加第一个阶梯
        this.form.tieredConfigs.push({
          stage: 1,
          rate: 1.0,
          startEnergy: 0,
          endEnergy: 0 // 默认结束用量为0
        });
      }
    },

    // 删除阶梯配置
    removeTieredConfig(index) {
      // 只允许从最后一级开始删除
      if (index !== this.form.tieredConfigs.length - 1) {
        this.$message.warning("只能从最后一级开始删除");
        return;
      }

      this.form.tieredConfigs.splice(index, 1);

      // 重新设置阶梯序号
      this.form.tieredConfigs.forEach((item, i) => {
        item.stage = i + 1;
      });

      // 如果还有阶梯，将最后一个阶梯的结束用量设为0
      if (this.form.tieredConfigs.length > 0) {
        const lastConfig = this.form.tieredConfigs[this.form.tieredConfigs.length - 1];
        lastConfig.endEnergy = 0;
      }
    },

    // 验证用量范围
    validateEnergyRange(row, index) {
      // 验证结束用量必须大于开始用量
      if (!row.startEnergy && row.startEnergy !== 0) {
        this.$message.error('请先设置开始用量');
        row.endEnergy = null;
        return;
      }

      if (!row.endEnergy && row.endEnergy !== 0) {
        return;
      }

      if (row.endEnergy <= row.startEnergy) {
        this.$message.error('结束用量必须大于开始用量');
        row.endEnergy = row.startEnergy + 1; // 自动设置为开始用量+1
        return;
      }
    },

    // 验证整个表单提交前的阶梯配置完整性
    validateTieredConfigs() {
      if (this.form.rateType !== '1' || this.form.tieredConfigs.length === 0) {
        return true;
      }

      // 检查是否有未完成的配置（除了最后一个阶梯的endEnergy可以为0外）
      for (let i = 0; i < this.form.tieredConfigs.length - 1; i++) {
        const item = this.form.tieredConfigs[i];
        if (item.rate === null || item.rate === undefined ||
            item.startEnergy === null || item.startEnergy === undefined ||
            item.endEnergy === null || item.endEnergy === undefined ||
            item.endEnergy === 0) {
          this.$message.error('请完成所有阶梯配置');
          return false;
        }
      }

      // 检查最后一个阶梯的基本字段
      const lastItem = this.form.tieredConfigs[this.form.tieredConfigs.length - 1];
      if (lastItem.rate === null || lastItem.rate === undefined ||
          lastItem.startEnergy === null || lastItem.startEnergy === undefined) {
        this.$message.error('请完成最后一个阶梯的基本配置');
        return false;
      }

      // 检查第一个阶梯是否从0开始
      if (this.form.tieredConfigs[0].startEnergy !== 0) {
        this.$message.error('第一个阶梯必须从0开始');
        return false;
      }

      // 检查阶梯是否连续
      for (let i = 0; i < this.form.tieredConfigs.length - 1; i++) {
        if (this.form.tieredConfigs[i].endEnergy !== this.form.tieredConfigs[i + 1].startEnergy) {
          this.$message.error('阶梯用量必须连续');
          return false;
        }
      }

      return true;
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // 验证分段和阶梯配置
          if (this.form.peakValleyFlag === '1' && this.form.peakValleyConfigs.length === 0) {
            this.$message.error('请添加分段配置');
            this.activeTab = 'ladder';
            return;
          }

          if (this.form.rateType === '1' && this.form.tieredConfigs.length === 0) {
            this.$message.error('请添加阶梯配置');
            this.activeTab = 'tiered';
            return;
          }

          // 验证分段配置的连续性和完整性
          if (this.form.peakValleyFlag === '1' && !this.validatePeakValleyConfigs()) {
            this.activeTab = 'ladder';
            return;
          }

          // 验证阶梯配置的连续性和完整性
          if (this.form.rateType === '1' && !this.validateTieredConfigs()) {
            this.activeTab = 'tiered';
            return;
          }

          // 如果是修改操作且是阶梯计费，检查时间是否被修改
          if (this.form.id != null && this.form.rateType === '1') {
            getBillingConfig(this.form.id).then(response => {
              const oldData = response.data;
              // 检查开始时间或结束时间是否被修改
              if (oldData.startTime !== this.form.startTime || oldData.endTime !== this.form.endTime) {
                this.$modal.confirm('修改阶梯计费的时间将导致下次抄表时表具的阶梯累计量清零，从第一阶段重新开始累计计费，是否继续？').then(() => {
                  this.submitBillingConfig();
                }).catch(() => {
                  this.buttonLoading = false;
                });
              } else {
                this.submitBillingConfig();
              }
            });
          } else {
            this.submitBillingConfig();
          }
        }
      });
    },

    // 提交计费配置
    submitBillingConfig() {
      this.buttonLoading = true;
      if (this.form.id != null) {
        updateBillingConfig(this.form).then(response => {
          this.$modal.msgSuccess('修改成功');
          this.open = false;
          this.getList();
        }).finally(() => {
          this.buttonLoading = false;
        });
      } else {
        addBillingConfig(this.form).then(response => {
          this.$modal.msgSuccess('新增成功');
          this.open = false;
          this.getList();
        }).finally(() => {
          this.buttonLoading = false;
        });
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除？').then(() => {
        this.loading = true
        return delBillingConfig(ids)
      }).then(() => {
        this.loading = false
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      }).finally(() => {
        this.loading = false
      })
    },
    // 处理计费类型变更
    handleRateTypeChange(val) {
      if (val === '0') {
        // 标准计费，清空阶梯配置
        this.form.tieredConfigs = []
      }
    },
    // 处理是否分段变更
    handlePeakValleyFlagChange(val) {
      if (val === '0') {
        // 不分段，清空分段配置
        this.form.peakValleyConfigs = []
      }
    },
    // 添加分段配置
    addLadderConfig() {
      // 检查是否有未完成的配置
      if (this.form.peakValleyConfigs.length > 0) {
        const lastConfig = this.form.peakValleyConfigs[this.form.peakValleyConfigs.length - 1];

        // 检查最后一项是否已完成
        if (!lastConfig.startTime ||
            !lastConfig.endTime ||
            !lastConfig.type ||
            lastConfig.price === null ||
            lastConfig.price === undefined ||
            lastConfig.price === '' ||
            lastConfig.price < 0) {
          this.$message.warning("请先完成当前时段的配置");
          return;
        }

        // 检查结束时间是否大于开始时间
        const startTimeArr = lastConfig.startTime.split(':').map(Number);
        const endTimeArr = lastConfig.endTime.split(':').map(Number);

        const startSeconds = startTimeArr[0] * 3600 + startTimeArr[1] * 60 + startTimeArr[2];
        let endSeconds = endTimeArr[0] * 3600 + endTimeArr[1] * 60 + endTimeArr[2];

        // 特殊处理00:00:00，将其视为一天的结束
        if (endTimeArr[0] === 0 && endTimeArr[1] === 0 && endTimeArr[2] === 0) {
          endSeconds = 24 * 3600;
        }

        if (endSeconds <= startSeconds) {
          this.$message.warning("结束时间必须大于开始时间");
          return;
        }

        // 获取最后一个时间段的结束时间作为新时段的开始时间
        const startTime = lastConfig.endTime;

        // 如果最后一个时间段已经到00:00:00，则不允许再添加
        if (startTime === "00:00:00") {
          this.$message.warning("已覆盖全天时间段，无法再添加");
          return;
        }

        // 计算默认的结束时间（如果距离00:00:00不足3小时，则直接设为00:00:00）
        let endTime;
        const startTimeParts = startTime.split(':').map(Number);
        const startTotalMinutes = startTimeParts[0] * 60 + startTimeParts[1];

        if (24 * 60 - startTotalMinutes <= 180) { // 不足3小时
          endTime = "00:00:00";
        } else {
          // 默认增加3小时
          const endHour = startTimeParts[0] + 3;
          endTime = endHour >= 24 ? "00:00:00" : `${endHour.toString().padStart(2, '0')}:00:00`;
        }

        this.form.peakValleyConfigs.push({
          startTime: startTime,
          endTime: endTime,
          type: '0',
          price: 0
        });
      } else {
        // 添加第一个时段
        this.form.peakValleyConfigs.push({
          startTime: "00:00:00",
          endTime: "08:00:00", // 默认8小时
          type: '0',
          price: 0
        });
      }
    },

    // 删除分段配置
    removeLadderConfig(index) {
      // 如果删除的不是最后一个，需要调整后续时间段的开始时间
      if (index < this.form.peakValleyConfigs.length - 1) {
        const currentStartTime = this.form.peakValleyConfigs[index].startTime;
        this.form.peakValleyConfigs[index + 1].startTime = currentStartTime;
      }

      this.form.peakValleyConfigs.splice(index, 1);
    }
  }
}
</script>
