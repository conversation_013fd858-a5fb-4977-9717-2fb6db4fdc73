import request from '@/utils/request'

// 查询计划变更列表
export function listPlanChange(query) {
  return request({
    url: '/biz/planChange/list',
    method: 'get',
    params: query
  })
}

// 查询计划变更详细
export function getPlanChange(id) {
  return request({
    url: '/biz/planChange/' + id,
    method: 'get'
  })
}

// 新增计划变更
export function addPlanChange(data) {
  return request({
    url: '/biz/planChange',
    method: 'post',
    data: data
  })
}

// 修改计划变更
export function updatePlanChange(data) {
  return request({
    url: '/biz/planChange',
    method: 'put',
    data: data
  })
}

// 删除计划变更
export function delPlanChange(id) {
  return request({
    url: '/biz/planChange/' + id,
    method: 'delete'
  })
}
