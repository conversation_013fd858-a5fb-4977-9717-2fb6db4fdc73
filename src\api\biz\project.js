import request from '@/utils/request'

// 查询项目信息列表
export function listProject(query) {
  return request({
    url: '/biz/project/list',
    method: 'get',
    params: query
  })
}

// 查询项目信息列表ALL
export function listAll(query) {
  return request({
    url: '/biz/project/listAll',
    method: 'get',
    params: query
  })
}

// 查询项目信息详细
export function getProject(id) {
  return request({
    url: '/biz/project/' + id,
    method: 'get'
  })
}

// 新增项目信息
export function addProject(data) {
  return request({
    url: '/biz/project',
    method: 'post',
    data: data
  })
}

// 修改项目信息
export function updateProject(data) {
  return request({
    url: '/biz/project',
    method: 'put',
    data: data
  })
}

// 删除项目信息
export function delProject(id) {
  return request({
    url: '/biz/project/' + id,
    method: 'delete'
  })
}

// 项目选项
export function projectOption(query) {
  return request({
    url: '/biz/project/option',
    method: 'get',
    params: query
  })
}

