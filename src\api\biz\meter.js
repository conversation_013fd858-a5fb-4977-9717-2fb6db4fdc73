import request from '@/utils/request'

// 查询表具列表
export function listMeter(query) {
  return request({
    url: '/biz/meter/list',
    method: 'get',
    params: query
  })
}

// 查询表具详细
export function getMeter(id) {
  return request({
    url: '/biz/meter/' + id,
    method: 'get'
  })
}

// 新增表具
export function addMeter(data) {
  return request({
    url: '/biz/meter',
    method: 'post',
    data: data
  })
}

// 修改表具
export function updateMeter(data) {
  return request({
    url: '/biz/meter',
    method: 'put',
    data: data
  })
}

// 删除表具
export function delMeter(id) {
  return request({
    url: '/biz/meter/' + id,
    method: 'delete'
  })
}

// 获取区域表具树结构
export function meterTree(query) {
  return request({
    url: '/biz/meter/meterTree',
    method: 'get',
    params: query
  })
}

// 表具预存
export function topUp(data) {
  return request({
    url: '/biz/meter/topUp',
    method: 'post',
    data: data
  })
}

// 退费
export function returnFee(data) {
  return request({
    url: '/biz/meter/returnFee',
    method: 'post',
    data: data
  })
}

// 自动扣费修改
export function editAutoPay(data) {
  return request({
    url: '/biz/meter/editAutoPay',
    method: 'put',
    data: data
  })
}

// 抄表
export function reading(meterIds) {
  return request({
    url: '/biz/meter/reading/' + meterIds,
    method: 'post'
  })
}
