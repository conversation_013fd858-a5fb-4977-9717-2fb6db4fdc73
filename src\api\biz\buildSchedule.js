import request from '@/utils/request'

// 查询施工进度列表
export function listBuildSchedule(query) {
  return request({
    url: '/biz/buildSchedule/list',
    method: 'get',
    params: query
  })
}

// 查询施工进度详细
export function getBuildSchedule(id) {
  return request({
    url: '/biz/buildSchedule/' + id,
    method: 'get'
  })
}

// 新增施工进度
export function addBuildSchedule(data) {
  return request({
    url: '/biz/buildSchedule',
    method: 'post',
    data: data
  })
}

// 修改施工进度
export function updateBuildSchedule(data) {
  return request({
    url: '/biz/buildSchedule',
    method: 'put',
    data: data
  })
}

// 删除施工进度
export function delBuildSchedule(id) {
  return request({
    url: '/biz/buildSchedule/' + id,
    method: 'delete'
  })
}
