<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--区域数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="areaName"
            placeholder="输入关键字以查找"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="meterTreeOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            default-expand-all
            highlight-current
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <!--账单数据-->
      <el-col :span="20" :xs="24">
        <!-- 搜索区域 -->
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="68px"
        >
          <el-form-item label="账单号" prop="billNo">
            <el-input v-model="queryParams.billNo" placeholder="请输入账单编号" clearable
                      @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="表具名称" prop="meterName">
            <el-input
              v-model="queryParams.meterName"
              placeholder="请输入表具名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="能源类型" prop="energyType">
            <el-select
              v-model="queryParams.energyType"
              placeholder="能源类型"
              clearable
              style="width: 100px"
            >
              <el-option
                v-for="dict in dict.type.energy_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="计费类型" prop="rateType">
            <el-select
              v-model="queryParams.rateType"
              placeholder="计费类型"
              clearable
              style="width: 100px"
            >
              <el-option
                v-for="dict in dict.type.bill_rate_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="峰平谷" prop="peakValleyFlag">
            <el-select
              v-model="queryParams.peakValleyFlag"
              placeholder="峰平谷"
              clearable
              style="width: 100px"
            >
              <el-option
                v-for="dict in dict.type.yes_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="缴费状态" prop="payStatus">
            <el-select
              v-model="queryParams.payStatus"
              placeholder="缴费状态"
              clearable
              style="width: 100px"
            >
              <el-option
                v-for="dict in dict.type.pay_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="抄表时间">
            <el-date-picker
              v-model="thisTimeRange"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                       v-hasPermi="['biz:bill:export']"
            >导出
            </el-button>
          </el-form-item>
        </el-form>
        <!-- 数据表格 -->
        <el-table v-loading="loading" :data="billList" border>
          <el-table-column label="账单编号" align="center" prop="billNo"/>
          <el-table-column label="表具名称" align="center" prop="meterName"/>
          <el-table-column label="能源类型" align="center" prop="energyType" width="80">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.energy_type" :value="scope.row.energyType"/>
            </template>
          </el-table-column>
          <el-table-column label="本次读数" align="center" prop="thisReading"/>
          <el-table-column label="抄表时间" align="center" prop="thisTime" width="180"/>
          <el-table-column label="计费类型" align="center" prop="rateType">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.bill_rate_type" :value="scope.row.rateType"/>
            </template>
          </el-table-column>
          <el-table-column label="峰平谷" align="center" prop="peakValleyFlag">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.yes_no" :value="scope.row.peakValleyFlag"/>
            </template>
          </el-table-column>
          <el-table-column label="能耗量" align="center" prop="energyVolume"/>
          <el-table-column label="能源单位" align="center" prop="energyUnit" width="80"/>
          <el-table-column label="结算金额(元)" align="center" prop="settlementAmount"/>
          <el-table-column label="缴费状态" align="center" prop="payStatus">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.pay_status" :value="scope.row.payStatus"/>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)"
                         v-hasPermi="['biz:bill:query']"
              >查看
              </el-button>
              <el-button v-if="scope.row.payStatus === '0'" size="mini" type="text" icon="el-icon-s-finance"
                         @click="handlePay(scope.row)"
                         v-hasPermi="['biz:bill:pay']"
              >缴费
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页组件 -->
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize"
                    @pagination="getList"
        />
      </el-col>
    </el-row>
    <!-- 账单详情对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <!-- 账单基本信息 -->
      <el-descriptions title="账单基本信息" :column="3" border v-loading="billLoading">
        <el-descriptions-item label="账单编号">{{ form.billNo }}</el-descriptions-item>
        <el-descriptions-item label="表具名称">{{ form.meterName }}</el-descriptions-item>
        <el-descriptions-item label="能源类型">
          <dict-tag :options="dict.type.energy_type" :value="form.energyType"/>
        </el-descriptions-item>
        <el-descriptions-item label="计费类型">
          <dict-tag :options="dict.type.bill_rate_type" :value="form.rateType"/>
        </el-descriptions-item>
        <el-descriptions-item label="上次读数">{{ form.lastReading }}</el-descriptions-item>
        <el-descriptions-item label="上次抄表时间">{{ form.lastTime }}</el-descriptions-item>
        <el-descriptions-item label="峰平谷">
          <dict-tag :options="dict.type.yes_no" :value="form.peakValleyFlag"/>
        </el-descriptions-item>
        <el-descriptions-item label="本次读数">{{ form.thisReading }}</el-descriptions-item>
        <el-descriptions-item label="本次抄表时间">{{ form.thisTime }}</el-descriptions-item>
        <el-descriptions-item label="抄表方式">
          <dict-tag :options="dict.type.reading_way" :value="form.readingWay"/>
        </el-descriptions-item>
        <el-descriptions-item label="阶梯累计量" v-if="form.rateType === '1'">
          {{ form.finalCumulativeVolume }} {{ form.energyUnit }}
        </el-descriptions-item>
        <el-descriptions-item label="计费周期">
          {{ form.cycles }}
        </el-descriptions-item>
        <el-descriptions-item label="能耗量">{{ form.energyVolume }} {{ form.energyUnit }}</el-descriptions-item>
        <el-descriptions-item label="结算金额">{{ form.settlementAmount }} 元</el-descriptions-item>
        <el-descriptions-item label="缴费状态">
          <dict-tag :options="dict.type.pay_status" :value="form.payStatus"/>
        </el-descriptions-item>
        <el-descriptions-item label="缴费方式" v-if="form.payStatus === '1'">
          <dict-tag :options="dict.type.pay_way" :value="form.payWay"/>
        </el-descriptions-item>
        <el-descriptions-item label="缴费时间" v-if="form.payStatus === '1'">{{ form.payTime }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ form.createTime }}</el-descriptions-item>
      </el-descriptions>

      <!-- 账单明细表格 -->
      <div class="bill-detail-title">账单明细</div>
      <el-table :data="detailList" border style="width: 100%" max-height="300" v-loading="detailLoading">
        <el-table-column label="阶梯阶段" align="center" prop="phase" v-if="form.rateType === '1'"/>
        <el-table-column label="阶梯上限" align="center" prop="maxVolume" v-if="form.rateType === '1'">
          <template slot-scope="scope">
            {{ scope.row.maxVolume ? scope.row.maxVolume + ' ' + scope.row.energyUnit : '无上限' }}
          </template>
        </el-table-column>
        <el-table-column label="分段类型" align="center" prop="timeType" v-if="form.peakValleyFlag === '1'">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.peak_valley_type" :value="scope.row.timeType"/>
          </template>
        </el-table-column>
        <el-table-column label="分段范围" align="center" prop="timeRange" v-if="form.peakValleyFlag === '1'"/>
        <el-table-column label="能耗量" align="center" prop="volume">
          <template slot-scope="scope">
            {{ scope.row.volume }} {{ scope.row.energyUnit }}
          </template>
        </el-table-column>
        <el-table-column label="基价" align="center" prop="basePrice"/>
        <el-table-column label="峰谷加价" align="center" prop="addPrice" v-if="form.peakValleyFlag === '1'"/>
        <el-table-column label="合计单价" align="center" prop="unitPrice"/>
        <el-table-column label="金额" align="center" prop="fee"/>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listBill, getBill, pay } from '@/api/biz/bill'
import { listBillDetail } from '@/api/biz/billDetail'
import { meterTree } from '@/api/biz/meter'

export default {
  name: 'Bill',
  dicts: ['bill_rate_type', 'yes_no', 'pay_status', 'pay_way', 'charge_type', 'peak_valley_type', 'energy_type', 'reading_way'],
  data() {
    return {
      // 区域名称
      areaName: undefined,
      meterTreeOptions: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },

      thisTimeRange: [],
      // 遮罩层
      billLoading: true,
      detailLoading: true,
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 账单表格数据
      billList: [],
      // 账单明细表格数据
      detailList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        billNo: null,
        meterId: null,
        meterName: null,
        energyType: null,
        rateType: null,
        peakValleyFlag: null,
        payStatus: null,
        startThisTime: null,
        endThisTime: null
      },
      // 表单参数
      form: {}
    }
  },
  watch: {
    // 根据名称筛选
    areaName(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getMeterTree()
    this.getList()
  },
  methods: {
    // 获取区域表具树结构
    getMeterTree() {
      meterTree().then(res => {
        this.meterTreeOptions = res.data
      })
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 节点单击事件
    handleNodeClick(data) {
      // 如果是区域节点,不查询,直接返回
      if (data.type === 'area') {
        return
      }
      this.queryParams.meterId = data.id
      this.handleQuery()
    },
    // 缴费
    handlePay(row) {
      const id = row.id
      pay({
        billId: id
      }).then(res => {
        this.getList()
        this.$modal.msgSuccess('缴费成功')
      })
    },
    /** 查询账单列表 */
    getList() {
      this.loading = true
      listBill(this.queryParams).then(response => {
        this.billList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset()
      const id = row.id
      this.billLoading = true
      getBill(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改账单'
        this.billLoading = false
      })
      this.detailLoading = true
      listBillDetail({
        billId: id
      }).then(response => {
        this.detailList = response.data || []
        this.open = true
        this.title = '查看账单详情'
        this.detailLoading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        billNo: null,
        meterId: null,
        meterName: null,
        energyType: null,
        readingWay: null,
        lastReading: null,
        lastTime: null,
        thisReading: null,
        thisTime: null,
        rateType: '0',
        peakValleyFlag: '0',
        energyVolume: null,
        energyUnit: null,
        settlementAmount: null,
        payStatus: '0',
        payWay: null,
        payTime: null,
        createTime: null,
        finalCumulativeVolume: null,
        cycles: null
      }
      this.detailList = []
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      if (this.thisTimeRange && this.thisTimeRange.length === 2) {
        this.queryParams.startThisTime = this.thisTimeRange[0]
        this.queryParams.endThisTime = this.thisTimeRange[1]
      } else {
        this.queryParams.startThisTime = null
        this.queryParams.endThisTime = null
      }
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.thisTimeRange = []
      this.queryParams.meterId = null
      this.$refs.tree.setCurrentKey(null)
      this.handleQuery()
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('biz/bill/export', {
        ...this.queryParams
      }, `账单_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>

<style scoped>
.bill-detail-title {
  font-size: 16px;
  font-weight: bold;
  margin: 20px 0 10px;
  padding-left: 10px;
  border-left: 4px solid #409EFF;
}
</style>
