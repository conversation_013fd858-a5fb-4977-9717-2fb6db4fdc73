import request from '@/utils/request'

// 查询设备入库记录列表
export function listFacilityPutStorage(query) {
  return request({
    url: '/biz/facilityPutStorage/list',
    method: 'get',
    params: query
  })
}

// 查询设备入库记录详细
export function getFacilityPutStorage(id) {
  return request({
    url: '/biz/facilityPutStorage/' + id,
    method: 'get'
  })
}

// 新增设备入库记录
export function addFacilityPutStorage(data) {
  return request({
    url: '/biz/facilityPutStorage',
    method: 'post',
    data: data
  })
}

// 修改设备入库记录
export function updateFacilityPutStorage(data) {
  return request({
    url: '/biz/facilityPutStorage',
    method: 'put',
    data: data
  })
}

// 删除设备入库记录
export function delFacilityPutStorage(id) {
  return request({
    url: '/biz/facilityPutStorage/' + id,
    method: 'delete'
  })
}
