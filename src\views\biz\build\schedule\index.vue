<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="施工计划" prop="buildPlanId">
        <el-select v-model="queryParams.buildPlanId" clearable placeholder="请选择">
          <el-option
            v-for="dict in buildPlanOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['biz:buildSchedule:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['biz:buildSchedule:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['biz:buildSchedule:remove']"
        >删除
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="buildScheduleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="施工计划" align="center" prop="buildPlanName"/>
      <el-table-column label="今日工作" align="center" prop="todayWork"/>
      <el-table-column label="计划工作" align="center" prop="planWork"/>
      <el-table-column label="现场照片" align="center" prop="ossIds">
        <template slot-scope="scope">
          <image-preview :width=80 :height=80 :src="scope.row.ossIds"/>
        </template>
      </el-table-column>
      <el-table-column label="上报时间" align="center" prop="reportTime" width="180"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['biz:buildSchedule:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['biz:buildSchedule:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改施工进度对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="施工计划" prop="buildPlanId">
          <el-select v-model="form.buildPlanId" style="width: 100%" placeholder="请选择">
            <el-option
              v-for="dict in buildPlanOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="今日工作" prop="todayWork">
          <el-input v-model="form.todayWork" placeholder="请输入今日工作"/>
        </el-form-item>
        <el-form-item label="计划工作" prop="planWork">
          <el-input v-model="form.planWork" placeholder="请输入计划工作"/>
        </el-form-item>
        <el-form-item label="现场照片" prop="ossIds">
          <image-upload v-model="form.ossIds"/>
        </el-form-item>
        <el-form-item label="上报时间" prop="reportTime">
          <el-date-picker clearable
                          v-model="form.reportTime"
                          type="datetime"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          placeholder="请选择上报时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listBuildSchedule,
  getBuildSchedule,
  delBuildSchedule,
  addBuildSchedule,
  updateBuildSchedule
} from "@/api/biz/buildSchedule";
import {buildPlanOption} from "@/api/biz/buildPlan";

export default {
  name: "BuildSchedule",
  data() {
    return {
      // 项目id
      projectId: localStorage.getItem('projectId'),
      // 施工计划选项
      buildPlanOptions: [],
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 施工进度表格数据
      buildScheduleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        buildPlanId: undefined,
        todayWork: undefined,
        planWork: undefined,
        ossIds: undefined,
        projectId: undefined,
        reportTime: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        buildPlanId: [
          {required: true, message: "施工计划不能为空", trigger: "blur"}
        ],
        todayWork: [
          {required: true, message: "今日工作不能为空", trigger: "blur"}
        ],
        planWork: [
          {required: true, message: "计划工作不能为空", trigger: "blur"}
        ],
        reportTime: [
          {required: true, message: "上报时间不能为空", trigger: "blur"}
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getBuildPlanOption();
  },
  methods: {
    // 施工计划选项
    getBuildPlanOption() {
      buildPlanOption({
        projectId: this.projectId
      }).then(res => {
        this.buildPlanOptions = res.data;
      })
    },
    /** 查询施工进度列表 */
    getList() {
      this.loading = true;
      this.queryParams.projectId = this.projectId;
      listBuildSchedule(this.queryParams).then(response => {
        this.buildScheduleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        buildPlanId: undefined,
        todayWork: undefined,
        planWork: undefined,
        ossIds: undefined,
        projectId: undefined,
        reportTime: undefined,
        delFlag: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加施工进度";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getBuildSchedule(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.open = true;
        this.title = "修改施工进度";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          this.form.projectId = this.projectId;
          if (this.form.id != null) {
            updateBuildSchedule(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addBuildSchedule(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除施工进度编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delBuildSchedule(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    }
  }
};
</script>
