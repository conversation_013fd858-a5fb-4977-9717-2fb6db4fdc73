import request from '@/utils/request'

// 查询抄记录列表
export function listMeterReading(query) {
  return request({
    url: '/biz/meterReading/list',
    method: 'get',
    params: query
  })
}

// 查询抄记录详细
export function getMeterReading(id) {
  return request({
    url: '/biz/meterReading/' + id,
    method: 'get'
  })
}

// 新增抄记录
export function addMeterReading(data) {
  return request({
    url: '/biz/meterReading',
    method: 'post',
    data: data
  })
}

// 修改抄记录
export function updateMeterReading(data) {
  return request({
    url: '/biz/meterReading',
    method: 'put',
    data: data
  })
}

// 删除抄记录
export function delMeterReading(id) {
  return request({
    url: '/biz/meterReading/' + id,
    method: 'delete'
  })
}
