<template>
  <div class="time-analysis-selector">
    <el-form :model="formParams" size="small" :inline="true">
      <el-form-item>
        <slot name="front"></slot>
      </el-form-item>
      <el-form-item label="分析方式" prop="analysisType">
        <el-select
          v-model="formParams.analysisType"
          placeholder="分析方式"
          clearable
          style="width: 100%"
          @change="handleAnalysisTypeChange"
        >
          <el-option
            v-for="option in analysisTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间范围" prop="dateRange" v-if="formParams.analysisType">
        <!-- 年度分析时间选择器(两个单独的年份选择器) -->
        <div v-if="formParams.analysisType === '0'" class="year-range-container">
          <el-date-picker
            v-model="yearRange.startYear"
            type="year"
            placeholder="开始年份"
            value-format="yyyy"
            style="width: 120px"
            @change="handleYearRangeChange"
          ></el-date-picker>
          <span class="year-range-separator">至</span>
          <el-date-picker
            v-model="yearRange.endYear"
            type="year"
            placeholder="结束年份"
            value-format="yyyy"
            style="width: 120px"
            :picker-options="endYearPickerOptions"
            @change="handleYearRangeChange"
          ></el-date-picker>
        </div>

        <!-- 周分析时间选择器 -->
        <el-date-picker
          v-else-if="formParams.analysisType === '1'"
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptionsWeek"
          @change="handleDateRangeChange"
          style="width: 280px"
        ></el-date-picker>

        <!-- 月度分析时间选择器(范围，最多12个月) -->
        <el-date-picker
          v-else-if="formParams.analysisType === '2'"
          v-model="dateRange"
          type="monthrange"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          value-format="yyyy-MM"
          :picker-options="pickerOptionsMonth"
          @change="handleDateRangeChange"
          style="width: 280px"
        ></el-date-picker>

        <!-- 日分析时间选择器(范围，最多30天) -->
        <el-date-picker
          v-else-if="formParams.analysisType === '3'"
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptionsDay"
          @change="handleDateRangeChange"
          style="width: 280px"
        ></el-date-picker>

        <!-- 小时分析时间选择器(范围，最多24小时) -->
        <el-date-picker
          v-else-if="formParams.analysisType === '4'"
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          :picker-options="pickerOptionsHour"
          @change="handleDateRangeChange"
          style="width: 380px"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <slot name="actions"></slot>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "TimeAnalysisSelector",
  props: {
    // 初始分析类型
    initialAnalysisType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 分析类型选项，静态定义
      analysisTypeOptions: [
        { label: '年度', value: '0' },
        { label: '周', value: '1' },
        { label: '月度', value: '2' },
        { label: '日', value: '3' },
        { label: '小时', value: '4' }
      ],
      formParams: {
        analysisType: this.initialAnalysisType,
        startTime: undefined,
        endTime: undefined,
      },
      // 起始和结束时间范围
      dateRange: [],
      // 年范围选择
      yearRange: {
        startYear: '',
        endYear: '',
      },
      // 选择器状态
      pickerMinDate: '',
      // 年份选择器配置
      endYearPickerOptions: {
        disabledDate: (time) => {
          // 如果设置了开始年份，则只能选择大于等于开始年份且不超过开始年份+2年的年份（最多3年跨度）
          if (this.yearRange.startYear) {
            const startYear = parseInt(this.yearRange.startYear);
            const currentYear = time.getFullYear();
            return currentYear < startYear || currentYear > startYear + 2;
          }
          return false;
        }
      },
      // 周选择器配置
      pickerOptionsWeek: {
        onPick: ({ maxDate, minDate }) => {
          this.pickerMinDate = minDate.getTime();
        },
        disabledDate: (time) => {
          if (this.pickerMinDate) {
            // 计算天数差
            const dayDiff = Math.ceil((time.getTime() - this.pickerMinDate) / (1000 * 60 * 60 * 24));
            const isAfter = time.getTime() > this.pickerMinDate;
            const maxDays = 365; // 最多选择一年（365天）
            const tooFar = dayDiff > maxDays;
            if (isAfter && tooFar) {
              return true;
            }
          }
          return false;
        }
      },
      // 月份选择器配置
      pickerOptionsMonth: {
        onPick: ({ maxDate, minDate }) => {
          this.pickerMinDate = minDate.getTime();
        },
        disabledDate: (time) => {
          if (this.pickerMinDate) {
            const startDate = new Date(this.pickerMinDate);
            const endDate = new Date(time);
            // 计算月份差
            const months = (endDate.getFullYear() - startDate.getFullYear()) * 12 +
                          endDate.getMonth() - startDate.getMonth();
            const isAfter = time.getTime() > this.pickerMinDate;
            const maxMonths = 12; // 最多选择12个月
            const tooFar = months > maxMonths;
            if (isAfter && tooFar) {
              return true;
            }
          }
          return false;
        }
      },
      // 日期选择器配置
      pickerOptionsDay: {
        onPick: ({ maxDate, minDate }) => {
          this.pickerMinDate = minDate.getTime();
        },
        disabledDate: (time) => {
          if (this.pickerMinDate) {
            // 计算天数差
            const dayDiff = Math.ceil((time.getTime() - this.pickerMinDate) / (1000 * 60 * 60 * 24));
            const isAfter = time.getTime() > this.pickerMinDate;
            const maxDays = 30; // 最多选择30天
            const tooFar = dayDiff > maxDays;
            if (isAfter && tooFar) {
              return true;
            }
          }
          return false;
        }
      },
      // 小时选择器配置
      pickerOptionsHour: {
        onPick: ({ maxDate, minDate }) => {
          this.pickerMinDate = minDate.getTime();
        },
        disabledDate: (time) => {
          if (this.pickerMinDate) {
            // 计算小时差
            const hourDiff = Math.ceil((time.getTime() - this.pickerMinDate) / (1000 * 60 * 60));
            const isAfter = time.getTime() > this.pickerMinDate;
            const maxHours = 24; // 最多选择24小时
            const tooFar = hourDiff > maxHours;
            if (isAfter && tooFar) {
              return true;
            }
          }
          return false;
        }
      }
    };
  },
  created() {
    // 创建时如果有初始类型，自动设置默认值
    if (this.formParams.analysisType) {
      this.handleAnalysisTypeChange(this.formParams.analysisType);
    }
  },
  methods: {
    // 处理分析类型变更
    handleAnalysisTypeChange(val) {
      // 切换分析类型时重置时间范围
      this.dateRange = [];
      this.yearRange = {
        startYear: '',
        endYear: '',
      };
      // 重置日期选择器状态
      this.pickerMinDate = '';

      // 设置默认的时间范围
      if (val) {
        const now = new Date();
        switch (val) {
          case '0': // 年度
            // 默认选择当前年份和前一年
            const lastYear = now.getFullYear() - 1;
            const currentYear = now.getFullYear();
            this.yearRange.startYear = lastYear.toString();
            this.yearRange.endYear = currentYear.toString();
            this.dateRange = [lastYear.toString(), currentYear.toString()];
            break;
          case '1': // 周
            // 设置为最近30天
            const monthAgo = new Date(now);
            monthAgo.setDate(now.getDate() - 29); // 当前日期算一天，所以减29
            this.dateRange = [
              this.formatDate(monthAgo, 'yyyy-MM-dd'),
              this.formatDate(now, 'yyyy-MM-dd')
            ];
            break;
          case '2': // 月度
            // 默认选择当前月份和前一个月
            const currentMonth = this.formatDate(now, 'yyyy-MM');
            const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
            this.dateRange = [this.formatDate(lastMonth, 'yyyy-MM'), currentMonth];
            break;
          case '3': // 日
            // 设置为最近7天
            const weekAgoDay = new Date(now);
            weekAgoDay.setDate(now.getDate() - 6); // 当前日期算一天，所以减6
            this.dateRange = [
              this.formatDate(weekAgoDay, 'yyyy-MM-dd'),
              this.formatDate(now, 'yyyy-MM-dd')
            ];
            break;
          case '4': // 小时
            // 设置为当天的24小时
            const dayStart = new Date(now);
            dayStart.setHours(0, 0, 0, 0);
            const dayEnd = new Date(now);
            dayEnd.setHours(23, 59, 59, 999);
            this.dateRange = [
              this.formatDate(dayStart, 'yyyy-MM-dd HH:mm:ss'),
              this.formatDate(dayEnd, 'yyyy-MM-dd HH:mm:ss')
            ];
            break;
        }
      }

      // 更新查询参数
      this.updateTimeParams();

      // 触发事件通知父组件
      this.$emit('analysis-type-change', val);
      this.$emit('params-change', {
        analysisType: this.formParams.analysisType,
        startTime: this.formParams.startTime,
        endTime: this.formParams.endTime
      });
    },

    // 处理年份范围变更
    handleYearRangeChange() {
      if (this.yearRange.startYear && this.yearRange.endYear) {
        // 确保结束年份不小于开始年份
        const startYear = parseInt(this.yearRange.startYear);
        const endYear = parseInt(this.yearRange.endYear);

        if (endYear < startYear) {
          this.yearRange.endYear = this.yearRange.startYear;
        }

        // 确保年份范围不超过3年
        if (endYear - startYear > 2) {
          this.yearRange.endYear = (startYear + 2).toString();
        }

        // 更新dateRange，用于后续处理
        this.dateRange = [this.yearRange.startYear, this.yearRange.endYear];

        // 更新查询参数
        this.updateTimeParams();
      }
    },

    // 处理日期范围变更
    handleDateRangeChange(val) {
      // 更新查询参数
      this.updateTimeParams();
    },

    // 更新时间查询参数
    updateTimeParams() {
      // 根据不同分析类型和选择的日期范围，设置startTime和endTime
      const analysisType = this.formParams.analysisType;
      if (!analysisType || !this.dateRange) {
        // 触发事件通知父组件
        this.$emit('params-change', {
          analysisType: this.formParams.analysisType,
          startTime: undefined,
          endTime: undefined
        });
        return
      }

      switch (analysisType) {
        case '0': // 年度
          if (Array.isArray(this.dateRange) && this.dateRange.length === 2) {
            this.formParams.startTime = `${this.dateRange[0]}-01-01 00:00:00`;
            this.formParams.endTime = `${this.dateRange[1]}-12-31 23:59:59`;
          }
          break;
        case '1': // 周
          // 使用日期范围
          if (Array.isArray(this.dateRange) && this.dateRange.length === 2) {
            this.formParams.startTime = `${this.dateRange[0]} 00:00:00`;
            this.formParams.endTime = `${this.dateRange[1]} 23:59:59`;
          }
          break;
        case '2': // 月度
          if (Array.isArray(this.dateRange) && this.dateRange.length === 2) {
            // 获取开始月份的第一天
            this.formParams.startTime = `${this.dateRange[0]}-01 00:00:00`;

            // 获取结束月份的最后一天
            const [endYear, endMonth] = this.dateRange[1].split('-');
            const lastDay = new Date(endYear, endMonth, 0).getDate(); // 获取月份的最后一天
            this.formParams.endTime = `${this.dateRange[1]}-${lastDay} 23:59:59`;
          }
          break;
        case '3': // 日
          if (Array.isArray(this.dateRange) && this.dateRange.length === 2) {
            this.formParams.startTime = `${this.dateRange[0]} 00:00:00`;
            this.formParams.endTime = `${this.dateRange[1]} 23:59:59`;
          }
          break;
        case '4': // 小时
          if (Array.isArray(this.dateRange) && this.dateRange.length === 2) {
            this.formParams.startTime = this.dateRange[0];
            this.formParams.endTime = this.dateRange[1];
          }
          break;
      }

      // 触发事件通知父组件
      this.$emit('params-change', {
        analysisType: this.formParams.analysisType,
        startTime: this.formParams.startTime,
        endTime: this.formParams.endTime
      });
    },

    // 日期格式化工具
    formatDate(date, format) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return format
        .replace('yyyy', year)
        .replace('MM', month)
        .replace('dd', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
    },

    // 重置方法，供父组件调用
    reset() {
      this.formParams.analysisType = '';
      this.dateRange = [];
      this.yearRange = {
        startYear: '',
        endYear: '',
      };
      this.formParams.startTime = undefined;
      this.formParams.endTime = undefined;

      // 触发事件通知父组件
      this.$emit('params-change', {
        analysisType: this.formParams.analysisType,
        startTime: this.formParams.startTime,
        endTime: this.formParams.endTime
      });
    },

    // 获取当前参数方法，供父组件调用
    getParams() {
      return {
        analysisType: this.formParams.analysisType,
        startTime: this.formParams.startTime,
        endTime: this.formParams.endTime
      };
    }
  }
};
</script>

<style scoped>
.time-analysis-selector {
  margin-bottom: 10px;
}
.year-range-container {
  display: flex;
  align-items: center;
}
.year-range-separator {
  margin: 0 10px;
}
</style>
