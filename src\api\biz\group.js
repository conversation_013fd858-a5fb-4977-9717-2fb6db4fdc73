import request from '@/utils/request'

// 查询表具分组列表
export function listGroup(query) {
  return request({
    url: '/biz/group/list',
    method: 'get',
    params: query
  })
}

// 查询表具分组详细
export function getGroup(id) {
  return request({
    url: '/biz/group/' + id,
    method: 'get'
  })
}

// 新增表具分组
export function addGroup(data) {
  return request({
    url: '/biz/group',
    method: 'post',
    data: data
  })
}

// 修改表具分组
export function updateGroup(data) {
  return request({
    url: '/biz/group',
    method: 'put',
    data: data
  })
}

// 删除表具分组
export function delGroup(id) {
  return request({
    url: '/biz/group/' + id,
    method: 'delete'
  })
}

// 查询表具分组选项
export function optionGroup(query) {
  return request({
    url: '/biz/group/option',
    method: 'get',
    params: query
  })
}
