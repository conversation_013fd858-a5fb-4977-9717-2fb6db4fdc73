<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--能耗数据-->
      <el-col :span="24">
        <time-analysis-selector
          ref="timeAnalysisSelector"
          @params-change="handleTimeParamsChange"
        >
          <template #front>
            <el-form-item label="分组" prop="groupId">
              <el-select
                v-model="queryParams.groupId"
                placeholder="请选择"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="option in groupOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="点位类型" prop="pointType">
              <el-select
                v-model="queryParams.pointType"
                placeholder="请选择"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="option in dict.type.point_type"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </template>
          <template #actions>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </template>
        </time-analysis-selector>

        <!-- 第一行图表区域 -->
        <el-row :gutter="20">
          <!-- 左侧能耗占比饼图 -->
          <el-col :span="8">
            <div class="chart-container">
              <div class="chart-header">
                <span class="chart-title">能耗占比分析</span>
                <div class="chart-tools">
                  <el-tooltip content="下载图表" placement="top">
                    <i class="el-icon-download" @click="saveAsImage('energyPieChart')"></i>
                  </el-tooltip>
                </div>
              </div>
              <div ref="energyPieChart" class="chart-content"></div>
            </div>
          </el-col>

          <!-- 右侧能耗趋势柱状图 -->
          <el-col :span="16">
            <div class="chart-container">
              <div class="chart-header">
                <span class="chart-title">能耗趋势分析</span>
                <div class="chart-tools">
                  <el-tooltip content="查看数据" placement="top">
                    <i class="el-icon-view" @click="showDataView('energy')"></i>
                  </el-tooltip>
                  <el-tooltip content="下载图表" placement="top">
                    <i class="el-icon-download" @click="saveAsImage('energyBarChart')"></i>
                  </el-tooltip>
                </div>
              </div>
              <div ref="energyBarChart" class="chart-content"></div>
            </div>
          </el-col>
        </el-row>

        <!-- 第二行图表区域 -->
        <el-row :gutter="20">
          <!-- 左侧能耗排名柱状图 -->
          <el-col :span="8">
            <div class="chart-container">
              <div class="chart-header">
                <span class="chart-title">能耗排名</span>
                <div class="chart-tools">
                  <el-tooltip content="下载图表" placement="top">
                    <i class="el-icon-download" @click="saveAsImage('energyRankChart')"></i>
                  </el-tooltip>
                </div>
              </div>
              <div ref="energyRankChart" class="chart-content"></div>
            </div>
          </el-col>

          <!-- 右侧同比环比折线图 -->
          <el-col :span="16">
            <div class="chart-container">
              <div class="chart-header">
                <span class="chart-title">同比环比趋势图</span>
                <div class="chart-tools">
                  <el-tooltip content="查看数据" placement="top">
                    <i class="el-icon-view" @click="showDataView('compare')"></i>
                  </el-tooltip>
                  <el-tooltip content="下载图表" placement="top">
                    <i class="el-icon-download" @click="saveAsImage('compareLineChart')"></i>
                  </el-tooltip>
                </div>
              </div>
              <div ref="compareLineChart" class="chart-content"></div>
            </div>
          </el-col>
        </el-row>

        <!-- 数据视图对话框 -->
        <el-dialog :title="dataViewTitle" :visible.sync="dataViewVisible" width="50%">
          <el-table :data="dataViewData" border style="width: 100%">
            <el-table-column prop="date" label="日期" ></el-table-column>
            <el-table-column prop="energy" label="能耗值 (kWh)" ></el-table-column>
            <el-table-column prop="qoq" label="环比变化 (%)" >
              <template slot-scope="scope">
                <span :style="{color: scope.row.qoq >= 0 ? 'red' : 'green'}">
                  {{ scope.row.qoq >= 0 ? '+' + scope.row.qoq : scope.row.qoq }}%
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="yoy" label="同比变化 (%)" >
              <template slot-scope="scope">
                <span :style="{color: scope.row.yoy >= 0 ? 'red' : 'green'}">
                  {{ scope.row.yoy >= 0 ? '+' + scope.row.yoy : scope.row.yoy }}%
                </span>
              </template>
            </el-table-column>
          </el-table>
        </el-dialog>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { groupEnergyAnalyse, energyRatio } from '@/api/biz/analyse'
import TimeAnalysisSelector from '@/components/TimeAnalysisSelector/index.vue'
import { optionGroup } from '@/api/biz/group'

export default {
  name: 'analyseGroup',
  components: { TimeAnalysisSelector },
  dicts:['point_type'],
  data() {
    return {
      groupOptions: [],
      // 查询参数
      queryParams: {
        groupId: undefined,
        analysisType: undefined,
        startTime: undefined,
        endTime: undefined,
        pointType: undefined,
      },
      // 图表对象
      energyBarChart: null,
      compareLineChart: null,
      energyPieChart: null,
      energyRankChart: null,
      // 能耗数据
      energyData: {
        times: [],
        thisEnergy: [],
        qoqEnergy: [],
        yoyEnergy: [],
        qoqPercentage: [], // 环比百分比
        yoyPercentage: []  // 同比百分比
      },
      // 能耗占比数据
      energyRatioData: [],
      // 数据视图
      dataViewVisible: false,
      dataViewTitle: '能耗数据详情',
      dataViewData: [],
      // 图表颜色
      chartColors: {
        energy: ['#83bff6', '#188df0', '#188df0'],
        qoq: '#91CC75',
        yoy: '#EE6666',
        pie: ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272']
      }
    }
  },
  created() {
    this.getGroupOptions()
  },
  mounted() {
    this.$nextTick(() => {
      window.addEventListener('resize', this.resizeCharts)
      // 初始调整图表大小
      this.adjustChartsHeight()
    })
  },
  activated() {
    // 页面被激活时，也调整图表大小
    this.$nextTick(() => {
      this.adjustChartsHeight()
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeCharts)
    this.disposeCharts()
  },
  methods: {
    getGroupOptions() {
      optionGroup().then(res => {
        this.groupOptions = res.data
      })
    },
    // 处理时间参数变更
    handleTimeParamsChange(params) {
      this.queryParams.analysisType = params.analysisType
      this.queryParams.startTime = params.startTime
      this.queryParams.endTime = params.endTime
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // 检查必要参数
      if (!this.queryParams.groupId || !this.queryParams.analysisType ||
        !this.queryParams.startTime || !this.queryParams.endTime) {
        this.$message.warning('请选择分组、分析方式和时间范围')
        return
      }

      // 获取能耗分析数据
      this.getEnergyAnalyse()

      // 获取能耗占比数据
      this.getEnergyRatio()
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 重置时间选择器
      this.$refs.timeAnalysisSelector.reset()
      // 重置其他参数
      this.queryParams.groupId = undefined
    },
    // 获取能耗分析数据
    getEnergyAnalyse() {
      // 初始化时先清除旧图表
      if (this.energyBarChart) {
        this.energyBarChart.dispose()
        this.energyBarChart = null
      }
      if (this.compareLineChart) {
        this.compareLineChart.dispose()
        this.compareLineChart = null
      }

      // 清空内容
      this.$refs.energyBarChart.innerHTML = ''
      this.$refs.compareLineChart.innerHTML = ''

      // 使用Element UI Loading组件
      const barChartLoading = this.$loading({
        target: this.$refs.energyBarChart,
        text: '数据加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.7)'
      })

      const compareChartLoading = this.$loading({
        target: this.$refs.compareLineChart,
        text: '数据加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.7)'
      })

      groupEnergyAnalyse(this.queryParams).then(res => {
        if (res.data) {
          // 关闭loading
          barChartLoading.close()
          compareChartLoading.close()

          const { times, thisEnergy, qoqEnergy, yoyEnergy } = res.data
          this.energyData.times = times
          this.energyData.thisEnergy = thisEnergy
          this.energyData.qoqEnergy = qoqEnergy
          this.energyData.yoyEnergy = yoyEnergy

          // 计算环比百分比
          this.energyData.qoqPercentage = this.calculatePercentage(thisEnergy, qoqEnergy)

          // 计算同比百分比
          this.energyData.yoyPercentage = this.calculatePercentage(thisEnergy, yoyEnergy)

          // 延迟初始化图表，确保DOM已经渲染完成
          this.$nextTick(() => {
            // 初始化图表
            this.initEnergyBarChart()
            this.initCompareLineChart()

            // 准备数据视图数据
            this.prepareDataViewData()
          })
        }
      }).catch(error => {
        // 关闭loading
        barChartLoading.close()
        compareChartLoading.close()

        console.error('获取能耗分析数据失败:', error)
        this.$message.error('获取能耗分析数据失败')

        // 显示错误提示
        this.$refs.energyBarChart.innerHTML = '<div style="display:flex;justify-content:center;align-items:center;height:100%;color:#F56C6C;"><i class="el-icon-warning-outline" style="margin-right:5px;"></i>数据加载失败</div>'
        this.$refs.compareLineChart.innerHTML = '<div style="display:flex;justify-content:center;align-items:center;height:100%;color:#F56C6C;"><i class="el-icon-warning-outline" style="margin-right:5px;"></i>数据加载失败</div>'
      })
    },
    // 获取能耗占比数据
    getEnergyRatio() {
      // 初始化时先清除旧图表
      if (this.energyPieChart) {
        this.energyPieChart.dispose()
        this.energyPieChart = null
      }
      if (this.energyRankChart) {
        this.energyRankChart.dispose()
        this.energyRankChart = null
      }

      // 清空内容
      this.$refs.energyPieChart.innerHTML = ''
      this.$refs.energyRankChart.innerHTML = ''

      // 使用Element UI Loading组件
      const pieChartLoading = this.$loading({
        target: this.$refs.energyPieChart,
        text: '数据加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.7)'
      })

      const rankChartLoading = this.$loading({
        target: this.$refs.energyRankChart,
        text: '数据加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.7)'
      })

      energyRatio(this.queryParams).then(res => {
        if (res.data) {
          // 关闭loading
          pieChartLoading.close()
          rankChartLoading.close()

          this.energyRatioData = res.data

          // 延迟初始化图表，确保DOM已经渲染完成
          this.$nextTick(() => {
            // 初始化饼图和排名图
            this.initEnergyPieChart()
            this.initEnergyRankChart()
          })
        }
      }).catch(error => {
        // 关闭loading
        pieChartLoading.close()
        rankChartLoading.close()

        console.error('获取能耗占比数据失败:', error)
        this.$message.error('获取能耗占比数据失败')

        // 显示错误提示
        this.$refs.energyPieChart.innerHTML = '<div style="display:flex;justify-content:center;align-items:center;height:100%;color:#F56C6C;"><i class="el-icon-warning-outline" style="margin-right:5px;"></i>数据加载失败</div>'
        this.$refs.energyRankChart.innerHTML = '<div style="display:flex;justify-content:center;align-items:center;height:100%;color:#F56C6C;"><i class="el-icon-warning-outline" style="margin-right:5px;"></i>数据加载失败</div>'
      })
    },
    // 计算百分比
    calculatePercentage(current, compare) {
      return current.map((value, index) => {
        if (!compare[index] || compare[index] === 0) {
          return 0
        }
        // 计算百分比变化: (当前值 - 对比值) / 对比值 * 100
        return parseFloat(((value - compare[index]) / compare[index] * 100).toFixed(2))
      })
    },
    // 初始化能耗柱状图
    initEnergyBarChart() {
      if (this.energyBarChart) {
        this.energyBarChart.dispose()
      }
      this.energyBarChart = echarts.init(this.$refs.energyBarChart, 'macarons')

      const option = {
        title: {
          text: '',
          subtext: ''
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: (params) => {
            return `<div style="font-weight:bold">${params[0].axisValue}</div>
                   <div style="margin-top:5px">
                     <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${params[0].color}"></span>
                     能耗值: ${params[0].value.toFixed(2)} kWh
                   </div>`
          }
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        dataZoom: [],
        xAxis: {
          type: 'category',
          data: this.energyData.times,
          boundaryGap: true,
          axisLabel: {
            formatter: (value) => {
              // 日期格式化处理
              if (!value) return ''

              try {
                const date = new Date(value)
                if (isNaN(date.getTime())) {
                  return value
                }

                if (value.includes('-') || value.includes('/')) {
                  if (value.includes(':') || value.includes(' ')) {
                    return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}时`
                  } else if (value.length <= 7) {
                    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
                  } else {
                    return `${date.getMonth() + 1}/${date.getDate()}`
                  }
                } else if (value.length === 4) {
                  return value
                } else {
                  return value
                }
              } catch (e) {
                console.error('Date formatting error:', e)
                return value
              }
            },
            interval: 'auto',
            rotate: 0
          }
        },
        yAxis: {
          type: 'value',
          name: '能耗值',
          axisLabel: {
            formatter: '{value} kWh'
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: [
          {
            name: '能耗数值',
            type: 'bar',
            barWidth: '60%',
            data: this.energyData.thisEnergy,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#83bff6' },
                { offset: 0.5, color: '#188df0' },
                { offset: 1, color: '#188df0' }
              ])
            },
            emphasis: {
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#2378f7' },
                  { offset: 0.7, color: '#2378f7' },
                  { offset: 1, color: '#83bff6' }
                ])
              }
            },
            markLine: {
              symbol: 'none',
              lineStyle: {
                color: '#5470C6',
                type: 'dashed'
              },
              data: [
                {
                  type: 'average',
                  name: '平均值',
                  label: {
                    position: 'middle',
                    formatter: (params) => {
                      return `平均: ${params.value.toFixed(2)} kWh`
                    },
                    fontSize: 10,
                    color: '#5470C6',
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    padding: [2, 4],
                    borderRadius: 2,
                    distance: 10
                  }
                }
              ]
            }
          }
        ]
      }

      this.energyBarChart.setOption(option)
    },
    // 初始化同比环比折线图
    initCompareLineChart() {
      if (this.compareLineChart) {
        this.compareLineChart.dispose()
      }
      this.compareLineChart = echarts.init(this.$refs.compareLineChart, 'macarons')

      const option = {
        title: {
          text: '同比环比分析',
          left: 'center',
          top: 0,
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter: (params) => {
            let result = `<div style="font-weight:bold">${params[0].axisValue}</div>`

            for (let i = 0; i < params.length; i++) {
              const param = params[i]
              const value = param.value
              const sign = value >= 0 ? '+' : ''
              const color = value >= 0 ? 'red' : 'green'

              result += `<div style="margin: 3px 0">
                <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${param.color}"></span>
                ${param.seriesName}: <span style="color:${color}">${sign}${value}%</span>
              </div>`
            }

            return result
          }
        },
        legend: {
          data: ['环比变化', '同比变化'],
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '30px',
          top: '60px',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.energyData.times,
          axisLabel: {
            formatter: (value) => {
              if (!value) return ''

              try {
                const date = new Date(value)
                if (isNaN(date.getTime())) {
                  return value
                }

                if (value.includes('-') || value.includes('/')) {
                  if (value.includes(':') || value.includes(' ')) {
                    return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}时`
                  } else if (value.length <= 7) {
                    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
                  } else {
                    return `${date.getMonth() + 1}/${date.getDate()}`
                  }
                } else if (value.length === 4) {
                  return value
                } else {
                  return value
                }
              } catch (e) {
                console.error('Date formatting error:', e)
                return value
              }
            },
            interval: 'auto',
            rotate: 0
          }
        },
        yAxis: {
          type: 'value',
          name: '百分比',
          axisLabel: {
            formatter: '{value} %'
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: [
          {
            name: '环比变化',
            type: 'line',
            data: this.energyData.qoqPercentage,
            smooth: true,
            symbol: 'emptyCircle',
            symbolSize: 8,
            lineStyle: {
              width: 3,
              color: this.chartColors.qoq
            },
            itemStyle: {
              color: this.chartColors.qoq,
              borderWidth: 2
            },
            markLine: {
              data: []
            },
            areaStyle: {
              opacity: 0.1,
              color: this.chartColors.qoq
            }
          },
          {
            name: '同比变化',
            type: 'line',
            data: this.energyData.yoyPercentage,
            smooth: true,
            symbol: 'emptyCircle',
            symbolSize: 8,
            lineStyle: {
              width: 3,
              color: this.chartColors.yoy
            },
            itemStyle: {
              color: this.chartColors.yoy,
              borderWidth: 2
            },
            markLine: {
              data: []
            },
            areaStyle: {
              opacity: 0.1,
              color: this.chartColors.yoy
            }
          }
        ]
      }

      this.compareLineChart.setOption(option)
    },
    // 初始化能耗占比饼图
    initEnergyPieChart() {
      // 获取容器高度来适应图表
      const chartDom = this.$refs.energyPieChart

      // 确保DOM元素存在且可见
      if (!chartDom || chartDom.offsetHeight === 0) {
        setTimeout(() => {
          this.initEnergyPieChart()
        }, 100)
        return
      }

      this.energyPieChart = echarts.init(chartDom, 'macarons')

      // 不再过滤掉总能耗数据，保留所有数据项
      const pieData = this.energyRatioData
        .filter(item => item.value !== null && item.value !== undefined) // 过滤掉无效数据
        .map((item, index) => {
          return {
            name: item.name,
            value: item.value || 0, // 确保value不为null或undefined
            itemStyle: {
              color: this.chartColors.pie[index % this.chartColors.pie.length]
            }
          }
        })

      // 计算总能耗
      const totalEnergy = pieData.reduce((sum, item) => sum + item.value, 0)

      // 计算每项占比百分比，处理总能耗为0的情况
      pieData.forEach(item => {
        if (totalEnergy === 0) {
          // 如果总能耗为0，所有项都设为0%
          item.percentage = '0.0%'
        } else {
          item.percentage = ((item.value / totalEnergy) * 100).toFixed(1) + '%'
        }
      })

      // 如果没有数据或总能耗为0，显示无数据提示
      if (pieData.length === 0 || totalEnergy === 0) {
        this.$refs.energyPieChart.innerHTML = '<div style="display:flex;justify-content:center;align-items:center;height:100%;color:#909399;"><i class="el-icon-info-circle" style="margin-right:5px;"></i>暂无能耗数据</div>'
        return
      }

      const option = {
        title: {
          left: 'center',
          top: 0,
          subtext: `总能耗: ${totalEnergy.toFixed(2)} kWh`,
          subtextStyle: {
            fontSize: 12
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            // 防止NaN出现在tooltip中
            const percent = isNaN(params.percent) ? 0 : params.percent
            return `${params.name}<br/>能耗值: ${params.value.toFixed(2)} kWh<br/>占比: ${percent.toFixed(1)}%`
          }
        },
        legend: {
          // 将图例放到顶部
          type: 'scroll',
          orient: 'horizontal',
          top: 40,
          left: 'center',
          width: '90%',
          itemGap: 12,
          // 图例分页导航
          pageButtonItemGap: 5,
          pageButtonGap: 5,
          pageButtonPosition: 'end',
          pageIconColor: '#409EFF',
          pageIconInactiveColor: '#C0C4CC',
          pageIconSize: 12,
          // 图例样式
          itemWidth: 15,
          itemHeight: 10,
          textStyle: {
            fontSize: 12,
            color: '#606266'
          },
          formatter: (name) => {
            const item = pieData.find(d => d.name === name)
            if (item) {
              // 简化显示：名称 + 百分比
              return `${name} (${item.percentage})`
            }
            return name
          }
        },
        // 确保动画生效
        animation: true,
        animationThreshold: 10,
        animationDuration: 1000,
        animationEasing: 'cubicOut',
        animationDelay: function (idx) {
          return idx * 100
        },
        series: [
          {
            name: '能耗占比',
            type: 'pie',
            // 调整饼图半径，让它在图例下方有足够空间显示
            radius: ['35%', '60%'],
            // 调整饼图中心位置，向下移动以给顶部图例腾出空间
            center: ['50%', '65%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 14,
                fontWeight: 'bold',
                formatter: (params) => {
                  // 防止NaN出现在hover标签中
                  const percent = isNaN(params.percent) ? 0 : params.percent
                  return `${params.name}\n${params.value.toFixed(2)} kWh\n${percent.toFixed(1)}%`
                }
              }
            },
            labelLine: {
              show: false
            },
            data: pieData
          }
        ]
      }

      // 先设置一个空的数据集
      this.energyPieChart.setOption({
        series: [{
          type: 'pie',
          radius: ['35%', '60%'],
          center: ['50%', '65%'],
          data: []
        }]
      })

      // 然后延迟一帧后设置真实数据，确保动画生效
      setTimeout(() => {
        this.energyPieChart.setOption(option)
      }, 20)

      // 确保初始化完成后立即调整大小
      this.$nextTick(() => {
        this.energyPieChart.resize()
      })
    },
    // 初始化能耗排名柱状图
    initEnergyRankChart() {
      // 获取容器
      const chartDom = this.$refs.energyRankChart

      // 确保DOM元素存在且可见
      if (!chartDom || chartDom.offsetHeight === 0) {
        setTimeout(() => {
          this.initEnergyRankChart()
        }, 100)
        return
      }

      this.energyRankChart = echarts.init(chartDom, 'macarons')

      // 不再过滤掉总能耗数据，保留所有数据并按照升序排列（从小到大）
      const rankData = this.energyRatioData
        .filter(item => item.value !== null && item.value !== undefined)
        .map(item => ({...item, value: item.value || 0}))  // 确保值有效
        .sort((a, b) => a.value - b.value)  // 改为升序排列

      // 计算图表高度，确保每个柱子有足够空间
      const barHeight = 30 // 每个柱子的高度
      const minHeight = 280 // 最小图表高度
      const dataLength = rankData.length
      const contentHeight = Math.max(minHeight, barHeight * dataLength + 60) // 60px为顶部和底部的空间

      // 如果没有数据，显示无数据提示
      if (rankData.length === 0) {
        this.$refs.energyRankChart.innerHTML = '<div style="display:flex;justify-content:center;align-items:center;height:100%;color:#909399;"><i class="el-icon-info-circle" style="margin-right:5px;"></i>暂无能耗数据</div>'
        return
      }

      // 动态设置图表容器高度
      this.$refs.energyRankChart.style.height = contentHeight + 'px'

      // 首先设置基本选项（不包含数据）
      const baseOption = {
        title: {
          text: '分组能耗排名',
          left: 'center',
          top: 0,
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: (params) => {
            // tooltip中显示两位小数
            return `${params[0].name}: ${params[0].value.toFixed(2)} kWh`
          }
        },
        grid: {
          left: '3%',
          right: '15%',
          bottom: '3%',
          top: '60px',
          containLabel: true
        },
        dataZoom: [
          {
            type: 'slider',
            show: dataLength > 5,
            yAxisIndex: 0,
            width: 5,
            right: 10,
            start: 0,
            end: Math.min(100, (5 / dataLength) * 100),
            zoomLock: false,
            filterMode: 'filter'
          }
        ],
        animation: true,
        animationThreshold: 10,
        animationDuration: 1000,
        animationEasing: 'elasticOut',
        animationDelay: function (idx) {
          return idx * 120;
        },
        xAxis: {
          type: 'value',
          name: '',
          axisLabel: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        yAxis: {
          type: 'category',
          data: [],  // 先设置为空
          axisLabel: {
            fontSize: 14
          },
          animation: true,
          animationDuration: 1000,
          animationEasing: 'cubicOut'
        },
        series: [
          {
            name: '能耗值',
            type: 'bar',
            barWidth: barHeight,
            data: [],  // 先设置为空
            label: {
              show: true,
              position: 'right',
              formatter: (params) => {
                // 柱状图上的标签显示两位小数
                return `${params.value.toFixed(2)} kWh`
              }
            }
          }
        ]
      }

      // 先设置基本选项（无数据）
      this.energyRankChart.setOption(baseOption)

      // 延迟设置完整数据
      setTimeout(() => {
        const dataOption = {
          yAxis: {
            data: rankData.map(item => item.name)
          },
          series: [
            {
              data: rankData.map(item => {
                return {
                  value: item.value,
                  itemStyle: {
                    color: item.unique === true ? '#FAC858' : '#5470C6'
                  }
                }
              })
            }
          ]
        }

        this.energyRankChart.setOption(dataOption)
      }, 50)

      // 确保初始化完成后立即调整大小
      this.$nextTick(() => {
        this.energyRankChart.resize()
      })
    },
    // 准备数据视图数据
    prepareDataViewData() {
      this.dataViewData = this.energyData.times.map((time, index) => {
        return {
          date: time,
          energy: this.energyData.thisEnergy[index],
          qoq: this.energyData.qoqPercentage[index],
          yoy: this.energyData.yoyPercentage[index]
        }
      })
    },
    // 显示数据视图
    showDataView(type) {
      switch (type) {
        case 'energy':
          this.dataViewTitle = '能耗数据详情'
          break
        case 'compare':
          this.dataViewTitle = '同比环比数据详情'
          break
        default:
          this.dataViewTitle = '能耗数据详情'
      }
      this.dataViewVisible = true
    },
    // 调整所有图表大小
    resizeCharts() {
      if (this.energyBarChart) {
        this.energyBarChart.resize()
      }
      if (this.compareLineChart) {
        this.compareLineChart.resize()
      }
      if (this.energyPieChart) {
        this.energyPieChart.resize()
      }
      if (this.energyRankChart) {
        this.energyRankChart.resize()
      }

      // 调用额外的高度调整
      this.adjustChartsHeight()
    },
    // 调整第一行图表容器高度，确保对齐
    adjustChartsHeight() {
      this.$nextTick(() => {
        // 获取第一行所有图表容器
        const firstRowContainers = this.$el.querySelectorAll('.el-row:first-of-type .chart-container')
        if (firstRowContainers && firstRowContainers.length >= 2) {
          // 重新设置高度为相同值
          const maxHeight = Math.max(...Array.from(firstRowContainers).map(el => el.scrollHeight))
          if (maxHeight > 0) {
            firstRowContainers.forEach(container => {
              container.style.height = maxHeight + 'px'
            })
          }

          // 调整图表大小以适应新容器
          if (this.energyPieChart) {
            this.energyPieChart.resize()
          }
          if (this.energyBarChart) {
            this.energyBarChart.resize()
          }
        }
      })
    },
    // 销毁所有图表
    disposeCharts() {
      if (this.energyBarChart) {
        this.energyBarChart.dispose()
        this.energyBarChart = null
      }
      if (this.compareLineChart) {
        this.compareLineChart.dispose()
        this.compareLineChart = null
      }
      if (this.energyPieChart) {
        this.energyPieChart.dispose()
        this.energyPieChart = null
      }
      if (this.energyRankChart) {
        this.energyRankChart.dispose()
        this.energyRankChart = null
      }
    },
    // 保存图表为图片
    saveAsImage(chartRef) {
      let chart = null
      switch (chartRef) {
        case 'energyBarChart':
          chart = this.energyBarChart
          break
        case 'compareLineChart':
          chart = this.compareLineChart
          break
        case 'energyPieChart':
          chart = this.energyPieChart
          break
        case 'energyRankChart':
          chart = this.energyRankChart
          break
      }

      if (chart) {
        const url = chart.getDataURL({
          pixelRatio: 2,
          backgroundColor: '#fff'
        })

        const link = document.createElement('a')
        link.download = `分组能源分析_${chartRef}_${new Date().getTime()}.png`
        link.href = url
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }
    }
  }
}
</script>

<style scoped>
.chart-container {
  background: #fff;
  padding: 20px;
  margin-top: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: visible;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.chart-tools {
  display: flex;
  gap: 15px;
}

.chart-tools i {
  font-size: 16px;
  cursor: pointer;
  color: #606266;
}

.chart-tools i:hover {
  color: #409EFF;
}

.chart-content {
  width: 100%;
  height: 280px;
  position: relative;
}

/* 第一行中的图表容器样式，固定高度确保对齐 */
.el-row:first-of-type .chart-container {
  height: 380px; /* 增加高度，为图例提供更多空间 */
  padding-top: 10px;
  padding-bottom: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.el-row:first-of-type .chart-container .chart-content {
  flex: 1;
}

/* 第二行中的图表容器样式，也固定高度保持一致 */
.el-row:nth-of-type(2) .chart-container {
  height: 360px;
  padding-top: 10px;
  padding-bottom: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.el-row:nth-of-type(2) .chart-container .chart-content {
  flex: 1;
}
</style>
