import request from '@/utils/request'

// 查询设备请领列表
export function listFacilityGetStorage(query) {
  return request({
    url: '/biz/facilityGetStorage/list',
    method: 'get',
    params: query
  })
}

// 查询设备请领详细
export function getFacilityGetStorage(id) {
  return request({
    url: '/biz/facilityGetStorage/' + id,
    method: 'get'
  })
}

// 新增设备请领
export function addFacilityGetStorage(data) {
  return request({
    url: '/biz/facilityGetStorage',
    method: 'post',
    data: data
  })
}

// 修改设备请领
export function updateFacilityGetStorage(data) {
  return request({
    url: '/biz/facilityGetStorage',
    method: 'put',
    data: data
  })
}

// 删除设备请领
export function delFacilityGetStorage(id) {
  return request({
    url: '/biz/facilityGetStorage/' + id,
    method: 'delete'
  })
}

// 查询设备请领记录列表
export function listFacilityGetReading(query) {
  return request({
    url: '/biz/facilityGetReading/list',
    method: 'get',
    params: query
  })
}
