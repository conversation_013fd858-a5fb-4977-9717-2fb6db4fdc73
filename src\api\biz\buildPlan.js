import request from '@/utils/request'

// 查询施工计划列表
export function listBuildPlan(query) {
  return request({
    url: '/biz/buildPlan/list',
    method: 'get',
    params: query
  })
}

// 查询施工计划详细
export function getBuildPlan(id) {
  return request({
    url: '/biz/buildPlan/' + id,
    method: 'get'
  })
}

// 新增施工计划
export function addBuildPlan(data) {
  return request({
    url: '/biz/buildPlan',
    method: 'post',
    data: data
  })
}

// 修改施工计划
export function updateBuildPlan(data) {
  return request({
    url: '/biz/buildPlan',
    method: 'put',
    data: data
  })
}

// 删除施工计划
export function delBuildPlan(id) {
  return request({
    url: '/biz/buildPlan/' + id,
    method: 'delete'
  })
}

// 施工计划选项
export function buildPlanOption(query) {
  return request({
    url: '/biz/buildPlan/option',
    method: 'get',
    params: query
  })
}
