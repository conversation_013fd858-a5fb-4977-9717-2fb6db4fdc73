import request from '@/utils/request'

// 表具能耗分析
export function meterEnergyAnalyse(query) {
  return request({
    url: '/biz/meterAnalyse/energyAnalyse',
    method: 'get',
    params: query
  })
}

// 能耗总汇
export function energySummary(query) {
  return request({
    url: '/biz/meterAnalyse/energySummary',
    method: 'get',
    params: query
  })
}

// 组能耗分析
export function groupEnergyAnalyse(query) {
  return request({
    url: '/biz/groupAnalyse/energyAnalyse',
    method: 'get',
    params: query
  })
}

// 能耗占比
export function energyRatio(query) {
  return request({
    url: '/biz/groupAnalyse/energyRatio',
    method: 'get',
    params: query
  })
}
