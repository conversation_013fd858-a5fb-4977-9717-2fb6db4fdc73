import request from '@/utils/request'

// 查询项目验收列表
export function listProjectCheck(query) {
  return request({
    url: '/biz/projectCheck/list',
    method: 'get',
    params: query
  })
}

// 查询项目验收详细
export function getProjectCheck(id) {
  return request({
    url: '/biz/projectCheck/' + id,
    method: 'get'
  })
}

// 新增项目验收
export function addProjectCheck(data) {
  return request({
    url: '/biz/projectCheck',
    method: 'post',
    data: data
  })
}

// 修改项目验收
export function updateProjectCheck(data) {
  return request({
    url: '/biz/projectCheck',
    method: 'put',
    data: data
  })
}

// 删除项目验收
export function delProjectCheck(id) {
  return request({
    url: '/biz/projectCheck/' + id,
    method: 'delete'
  })
}
