import request from '@/utils/request'

// 查询点位类型列表
export function listPointType(query) {
  return request({
    url: '/biz/pointType/list',
    method: 'get',
    params: query
  })
}

// 查询点位类型详细
export function getPointType(id) {
  return request({
    url: '/biz/pointType/' + id,
    method: 'get'
  })
}

// 新增点位类型
export function addPointType(data) {
  return request({
    url: '/biz/pointType',
    method: 'post',
    data: data
  })
}

// 修改点位类型
export function updatePointType(data) {
  return request({
    url: '/biz/pointType',
    method: 'put',
    data: data
  })
}

// 删除点位类型
export function delPointType(id) {
  return request({
    url: '/biz/pointType/' + id,
    method: 'delete'
  })
}

// 查询点位类型选项
export function pointTypeOption() {
  return request({
    url: '/biz/pointType/option',
    method: 'get'
  })
}
