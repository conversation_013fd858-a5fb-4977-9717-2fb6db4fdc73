import request from '@/utils/request'

// 查询计费配置列表
export function listBillingConfig(query) {
  return request({
    url: '/biz/billingConfig/list',
    method: 'get',
    params: query
  })
}

// 查询计费配置详细
export function getBillingConfig(id) {
  return request({
    url: '/biz/billingConfig/' + id,
    method: 'get'
  })
}

// 新增计费配置
export function addBillingConfig(data) {
  return request({
    url: '/biz/billingConfig',
    method: 'post',
    data: data
  })
}

// 修改计费配置
export function updateBillingConfig(data) {
  return request({
    url: '/biz/billingConfig',
    method: 'put',
    data: data
  })
}

// 删除计费配置
export function delBillingConfig(id) {
  return request({
    url: '/biz/billingConfig/' + id,
    method: 'delete'
  })
}
