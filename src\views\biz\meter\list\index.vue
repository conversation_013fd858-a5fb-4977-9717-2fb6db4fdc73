<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--区域数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="areaName"
            placeholder="请输入区域名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="areaOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            default-expand-all
            highlight-current
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <!--表具数据-->
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="68px"
        >
          <el-form-item label="名称" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="表具编号" prop="number">
            <el-input
              v-model="queryParams.number"
              placeholder="请输入表具编号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="房号" prop="roomNumber">
            <el-input
              v-model="queryParams.roomNumber"
              placeholder="请输入房号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="重点表具" prop="emphasisFlag">
            <el-select
              v-model="queryParams.emphasisFlag"
              placeholder="重点表具"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in dict.type.yes_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="能源类型" prop="energyType">
            <el-select
              v-model="queryParams.energyType"
              placeholder="能源类型"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in dict.type.energy_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="在线状态" prop="onlineStatus">
            <el-select
              v-model="queryParams.onlineStatus"
              placeholder="在线状态"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in dict.type.online_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['biz:meter:add']"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['biz:meter:edit']"
            >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['biz:meter:remove']"
            >删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['biz:meter:export']"
            >导出
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="info"
              plain
              icon="el-icon-reading"
              size="mini"
              :disabled="multiple"
              @click="handleReading"
              v-hasPermi="['biz:meter:reading']"
            >抄表
            </el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="meterList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center"/>
          <el-table-column label="名称" align="center" prop="name"/>
          <el-table-column label="表具编号" align="center" prop="number"/>
          <el-table-column label="房号" align="center" prop="roomNumber"/>
          <el-table-column label="区域" align="center" prop="areaName"/>
          <el-table-column label="能源类型" align="center" prop="energyType">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.energy_type" :value="scope.row.energyType"/>
            </template>
          </el-table-column>
          <el-table-column label="表具读数" align="center" prop="meterRecord"/>
          <el-table-column label="表具时间" align="center" prop="meterTime" width="180"/>
          <el-table-column label="在线状态" align="center" prop="onlineStatus">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.online_status" :value="scope.row.onlineStatus"/>
            </template>
          </el-table-column>
          <el-table-column label="重点表具" align="center" prop="emphasisFlag">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.yes_no" :value="scope.row.emphasisFlag"/>
            </template>
          </el-table-column>
<!--          <el-table-column label="网关" align="center" prop="gatewayName"/>-->
          <el-table-column label="同步时间" align="center" prop="syncTime" width="180"/>
          <el-table-column label="操作" align="center" fixed="right" width="180" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['biz:meter:edit']"
              >修改
              </el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['biz:meter:remove']"
              >删除
              </el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-wallet"
                @click="handleAccount(scope.row)"
                v-hasPermi="['biz:meterAccountLog:list']"
              >账户
              </el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-reading"
                @click="handleSingleReading(scope.row)"
                v-hasPermi="['biz:meter:reading']"
              >抄表
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改具对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称"/>
        </el-form-item>
        <el-form-item label="表具编号" prop="number">
          <el-input v-model="form.number" placeholder="请输入表具编号"/>
        </el-form-item>
        <el-form-item label="能源类型" prop="energyType">
          <el-select
            v-model="form.energyType"
            placeholder="能源类型"
            style="width: 100%"
          >
            <el-option
              v-for="dict in dict.type.energy_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="房号" prop="roomNumber">
          <el-input v-model="form.roomNumber" placeholder="请输入房号"/>
        </el-form-item>
        <el-form-item label="区域" prop="areaId">
          <el-cascader :options="areaOptions" v-model="form.areaId" :props="props"
                       filterable style="width: 100%"
          >
          </el-cascader>
        </el-form-item>
        <el-form-item label="重点表具" prop="emphasisFlag">
          <el-select
            v-model="form.emphasisFlag"
            placeholder="重点表具"
            style="width: 100%"
          >
            <el-option
              v-for="dict in dict.type.yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
<!--        <el-form-item label="网关" prop="gatewayId">
          <el-input v-model="form.gatewayId" placeholder="请选择网关"/>
        </el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 表具预存对话框 -->
    <el-dialog :title="'表具账户详情'" :visible.sync="accountOpen" width="900px" append-to-body>
      <el-tabs v-model="accountActiveTab">
        <el-tab-pane label="基础信息" name="info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="表具名称">{{ accountForm.name }}</el-descriptions-item>
            <el-descriptions-item label="表具编号">{{ accountForm.number }}</el-descriptions-item>
            <el-descriptions-item label="当前余额">{{ accountForm.balance }}</el-descriptions-item>
            <el-descriptions-item label="能源类型">
              <dict-tag :options="dict.type.energy_type" :value="accountForm.energyType"/>
            </el-descriptions-item>
            <el-descriptions-item label="房号">{{ accountForm.roomNumber }}</el-descriptions-item>
            <el-descriptions-item label="区域">{{ accountForm.areaName }}</el-descriptions-item>
            <el-descriptions-item label="自动扣费">
              <el-switch
                v-model="accountForm.autoPayFlag"
                @change="autoPayHandel"
                active-color="#13ce66"
                inactive-color="#ff4949"
                active-text="开"
                inactive-text="关"
                active-value="1"
                inactive-value="0">
              </el-switch>
            </el-descriptions-item>
          </el-descriptions>

          <div v-hasPermi="['biz:meter:topUp']">
            <el-divider content-position="center">账户充值</el-divider>

            <el-form ref="topUpForm" :model="topUpForm" :rules="topUpRules" label-width="80px">
              <el-form-item label="预存金额" prop="amount">
                <el-row :gutter="10">
                  <el-col :span="18">
                    <el-input-number v-model="topUpForm.amount" :min="0.01" :max="*********" :precision="2" :step="10" placeholder="请输入预存金额"
                                     style="width: 100%"
                    />
                  </el-col>
                  <el-col :span="3">
                    <el-button style="width: 100%" plain :loading="buttonLoading" type="primary" @click="submitTopUpForm">充值</el-button>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-form>
          </div>

          <div v-hasPermi="['biz:meter:returnFee']">
            <el-divider content-position="center">账户退费</el-divider>

            <el-form ref="returnForm" :model="returnForm" :rules="returnRules" label-width="80px">
              <el-form-item label="退费金额" prop="amount">
                <el-row :gutter="10">
                  <el-col :span="18">
                    <el-input-number v-model="returnForm.amount" :min="0.01" :max="Number(accountForm.balance)" :precision="2" :step="10" placeholder="请输入退费金额"
                                     style="width: 100%"
                    />
                  </el-col>
                  <el-col :span="3">
                    <el-button style="width: 100%" plain :loading="returnButtonLoading" type="warning" @click="submitReturnForm">退费</el-button>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-form>
          </div>


        </el-tab-pane>

        <el-tab-pane label="账户流水" name="log">
          <el-form :model="logQueryParams" size="small" :inline="true" style="margin-bottom: 10px">
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="dateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期时间"
                end-placeholder="结束日期时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                @change="handleDateRangeChange"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleLogQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetLogQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <el-table v-loading="logLoading" :data="logList" style="width: 100%" :max-height="400">
            <el-table-column label="变动金额" align="center" prop="changeAmount">
              <template slot-scope="scope">
                <span :style="{ color: scope.row.changeAmount >= 0 ? 'green' : 'red' }">{{ scope.row.changeAmount }}</span>
              </template>
            </el-table-column>
            <el-table-column label="类型" align="center" prop="type">
              <template slot-scope="scope">
                <el-tag :type="getTypeTagType(scope.row.type)">{{ getTypeLabel(scope.row.type) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="变动前余额" align="center" prop="beforeBalance"/>
            <el-table-column label="变动后余额" align="center" prop="afterBalance"/>
            <el-table-column label="变动时间" align="center" prop="createdTime" width="160"/>
            <el-table-column label="备注" align="center" prop="remark"/>
          </el-table>

          <pagination
            style="height: 30px"
            v-show="logTotal>0"
            :total="logTotal"
            :page.sync="logQueryParams.pageNum"
            :limit.sync="logQueryParams.pageSize"
            @pagination="getListLog"
          />
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelAccount">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMeter, getMeter, delMeter, addMeter, updateMeter, topUp, returnFee, editAutoPay, reading } from '@/api/biz/meter'
import { listArea } from '@/api/biz/area'
import { listLog } from '@/api/biz/meterAccountLog'

export default {
  name: 'Meter',
  dicts: ['yes_no', 'energy_type', 'online_status'],
  data() {
    return {
      // 区域名称
      areaName: undefined,
      areaOptions: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      props: {
        emitPath: false,
        value: 'id', // 绑定的值字段名
        label: 'name', // 显示的文字字段名
        children: 'children' // 子选项字段名
      },

      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 具表格数据
      meterList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        number: undefined,
        roomNumber: undefined,
        areaId: undefined,
        gatewayId: undefined,
        energyType: undefined,
        meterStatus: undefined,
        onlineStatus: undefined,
        emphasisFlag: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: '名称不能为空', trigger: 'blur' }
        ],
        number: [
          { required: true, message: '表具编号不能为空', trigger: 'blur' }
        ],
        roomNumber: [
          { required: true, message: '房号不能为空', trigger: 'blur' }
        ],
        areaId: [
          { required: true, message: '区域不能为空', trigger: 'blur' }
        ],
        energyType: [
          { required: true, message: '能源类型不能为空', trigger: 'change' }
        ],
        emphasisFlag: [
          { required: true, message: '是否重点表具不能为空', trigger: 'blur' }
        ]
      },

      // 是否显示账户弹出层
      accountOpen: false,
      // 账户详情当前激活标签
      accountActiveTab: 'info',
      // 账户详情表单参数
      accountForm: {
        id: undefined,
        name: undefined,
        number: undefined,
        balance: 0,
        energyType: undefined,
        roomNumber: undefined,
        areaName: undefined,
        autoPayFlag: undefined,
      },
      // 预存表单参数
      topUpForm: {
        meterId: undefined,
        amount: 0
      },
      // 预存表单校验
      topUpRules: {
        amount: [
          { required: true, message: '预存金额不能为空', trigger: 'blur' },
          { type: 'number', min: 0.01, message: '预存金额必须大于0', trigger: 'blur' }
        ]
      },

      // 退费按钮loading
      returnButtonLoading: false,
      // 退费表单参数
      returnForm: {
        meterId: undefined,
        amount: 0
      },
      // 退费表单校验
      returnRules: {
        amount: [
          { required: true, message: '退费金额不能为空', trigger: 'blur' },
          { type: 'number', min: 0.01, message: '退费金额必须大于0', trigger: 'blur' },
          { validator: (rule, value, callback) => {
              if (value > Number(this.accountForm.balance)) {
                callback(new Error('退费金额不能大于当前余额'));
              } else {
                callback();
              }
            }, trigger: 'blur' }
        ]
      },

      // 账户流水加载状态
      logLoading: false,
      // 账户流水列表
      logList: [],
      // 账户流水总条数
      logTotal: 0,
      // 账户流水查询参数
      logQueryParams: {
        pageNum: 1,
        pageSize: 10,
        meterId: undefined,
        startCreatedTime: undefined,
        endCreatedTime: undefined
      },
      // 时间范围选择器
      dateRange: [],
    }
  },
  watch: {
    // 根据名称筛选部门树
    areaName(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getList()
    this.getAreaTree()
  },
  methods: {
    // 修改自动扣费
    autoPayHandel(){
      editAutoPay({
        meterId: this.accountForm.id,
        autoPayFlag: this.accountForm.autoPayFlag,
      }).then(res => {
        this.$modal.msgSuccess('操作成功')
      })
    },
    // 查询账户流水
    getListLog(){
      this.logLoading = true
      listLog(this.logQueryParams).then(response => {
        this.logList = response.rows
        this.logTotal = response.total
        this.logLoading = false
      }).catch(() => {
        this.logLoading = false
      })
    },

    // 获取类型标签样式
    getTypeTagType(type) {
      switch(type) {
        case '1': return 'success'
        case '2': return 'danger'
        case '3': return 'warning'
        case '4': return 'info'
        default: return ''
      }
    },

    // 获取类型标签文本
    getTypeLabel(type) {
      switch(type) {
        case '1': return '充值'
        case '2': return '扣费'
        case '3': return '退费'
        case '4': return '手工调整'
        default: return '未知'
      }
    },
    /** 查询区域下拉树结构 */
    getAreaTree() {
      listArea().then(response => {
        this.areaOptions = response.data
      })
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.areaId = data.id
      this.handleQuery()
    },

    /** 查询表具列表 */
    getList() {
      this.loading = true
      listMeter(this.queryParams).then(response => {
        this.meterList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        number: undefined,
        roomNumber: undefined,
        areaId: undefined,
        gatewayId: undefined,
        energyType: undefined,
        meterStatus: undefined,
        onlineStatus: undefined,
        meterRecord: undefined,
        syncTime: undefined,
        emphasisFlag: undefined,
        delFlag: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.queryParams.areaId = undefined
      this.$refs.tree.setCurrentKey(null)
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加表具'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true
      this.reset()
      const id = row.id || this.ids
      getMeter(id).then(response => {
        this.loading = false
        this.form = response.data
        this.open = true
        this.title = '修改表具'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.buttonLoading = true
          if (this.form.id != null) {
            updateMeter(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            }).finally(() => {
              this.buttonLoading = false
            })
          } else {
            addMeter(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            }).finally(() => {
              this.buttonLoading = false
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除？').then(() => {
        this.loading = true
        return delMeter(ids)
      }).then(() => {
        this.loading = false
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      }).finally(() => {
        this.loading = false
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('biz/meter/export', {
        ...this.queryParams
      }, `表具_${new Date().getTime()}.xlsx`)
    },

    /** 账户按钮操作 */
    handleAccount(row) {
      this.accountForm = {
        ...row,
        balance: Number(row.balance) || 0
      }
      this.topUpForm = {
        meterId: row.id,
        amount: 0
      }
      this.returnForm = {
        meterId: row.id,
        amount: 0
      }
      this.logQueryParams = {
        pageNum: 1,
        pageSize: 10,
        meterId: row.id,
        startCreatedTime: undefined,
        endCreatedTime: undefined
      }
      this.dateRange = []
      this.accountOpen = true
      this.accountActiveTab = 'info'
      this.getListLog()
    },

    // 取消账户详情
    cancelAccount() {
      this.accountOpen = false
      this.accountForm = {
        id: undefined,
        name: undefined,
        number: undefined,
        balance: undefined,
        energyType: undefined,
        roomNumber: undefined,
        areaName: undefined
      }
      this.topUpForm = {
        meterId: undefined,
        amount: 0
      }
      this.returnForm = {
        meterId: undefined,
        amount: 0
      }
      this.logList = []
    },

    /** 提交预存表单 */
    submitTopUpForm() {
      this.$refs['topUpForm'].validate(valid => {
        if (valid) {
          this.$modal.confirm('确认要充值' + this.topUpForm.amount + '元吗？').then(() => {
            this.buttonLoading = true
            return topUp({
              meterId: this.topUpForm.meterId,
              amount: this.topUpForm.amount
            })
          }).then(response => {
            this.$modal.msgSuccess('充值成功')
            // 刷新表具信息
            this.getList()
            // 刷新当前表具信息
            getMeter(this.accountForm.id).then(response => {
              this.accountForm = response.data
            })
            // 刷新账户流水
            this.getListLog()
            // 重置充值金额
            this.topUpForm.amount = 0
          }).finally(() => {
            this.buttonLoading = false
          })
        }
      })
    },

    /** 提交退费表单 */
    submitReturnForm() {
      this.$refs['returnForm'].validate(valid => {
        if (valid) {
          this.$modal.confirm('确认要退费' + this.returnForm.amount + '元吗？').then(() => {
            this.returnButtonLoading = true
            return returnFee({
              meterId: this.returnForm.meterId,
              amount: this.returnForm.amount
            })
          }).then(response => {
            this.$modal.msgSuccess('退费成功')
            // 刷新表具信息
            this.getList()
            // 刷新当前表具信息
            getMeter(this.accountForm.id).then(response => {
              this.accountForm = response.data
            })
            // 刷新账户流水
            this.getListLog()
            // 重置退费金额
            this.returnForm.amount = 0
          }).finally(() => {
            this.returnButtonLoading = false
          })
        }
      })
    },

    // 时间范围选择器变化
    handleDateRangeChange(val) {
      if (val) {
        this.logQueryParams.startCreatedTime = val[0]
        this.logQueryParams.endCreatedTime = val[1]
      } else {
        this.logQueryParams.startCreatedTime = undefined
        this.logQueryParams.endCreatedTime = undefined
      }
    },

    // 搜索账户流水
    handleLogQuery() {
      this.logQueryParams.pageNum = 1
      this.getListLog()
    },

    // 重置账户流水查询参数
    resetLogQuery() {
      this.logQueryParams.startCreatedTime = undefined
      this.logQueryParams.endCreatedTime = undefined
      this.dateRange = []
      this.getListLog()
    },

    /** 批量抄表按钮操作 */
    handleReading() {
      const meterIds = this.ids
      if (meterIds.length === 0) {
        this.$modal.msgWarning('请选择要抄表的表具')
        return
      }
      this.$modal.confirm('是否确认对选中的' + meterIds.length + '个表具进行抄表操作？').then(() => {
        this.loading = true
        return reading(meterIds.join(','))
      }).then(() => {
        this.loading = false
        this.getList()
        this.$modal.msgSuccess('抄表操作成功')
      }).catch(() => {
        this.loading = false
      })
    },

    /** 单个抄表按钮操作 */
    handleSingleReading(row) {
      const meterId = row.id
      this.$modal.confirm('是否确认对表具"' + row.name + '"进行抄表操作？').then(() => {
        this.loading = true
        return reading(meterId)
      }).then(() => {
        this.loading = false
        this.getList()
        this.$modal.msgSuccess('抄表操作成功')
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>
